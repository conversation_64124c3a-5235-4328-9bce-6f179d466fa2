package com.nexla.connector.config;

import com.fasterxml.jackson.annotation.JsonCreator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public enum FlowType {
  STREAMING,
  IN_MEMORY,
  CUSTOM,
  REPLICATION,
  SPARK,
  RAG;

  private static final Logger LOGGER = LoggerFactory.getLogger(FlowType.class);

  @JsonCreator
  public static FlowType fromString(String key) {
    switch (key.toUpperCase()) {
      case "SPARK":
        return SPARK;
      case "REPLICATION":
        return REPLICATION;
      case "IN.MEMORY":
      case "IN_MEMORY":
        return IN_MEMORY;
      case "CUSTOM":
        return CUSTOM;
      case "STREAMING":
      case "KAFKA":
      case "CDC": // CDC is treated as STREAMING flow (See NEX-10977)
      case "ELT":
        return STREAMING;
      case "RAG":
        return RAG;
      default:
        LOGGER.error("Unknown FlowType: {}", key, new Exception("Stack trace"));
        return STREAMING;
    }
  }
}
