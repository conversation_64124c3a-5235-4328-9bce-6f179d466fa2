package com.nexla.connector.config.rest;

import static com.nexla.common.ConfigUtils.opt;
import static com.nexla.common.NexlaConstants.*;
import static com.nexla.connector.config.BaseConnectorConfig.*;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static com.nexla.connector.config.SourceConnectorConfig.CREDENTIAL_ENRICHMENT_URL;
import static java.util.Optional.empty;
import static java.util.Optional.ofNullable;
import static org.apache.kafka.common.config.ConfigDef.Importance.LOW;
import static org.springframework.http.HttpMethod.POST;

import com.bazaarvoice.jolt.JsonUtils;
import com.nexla.common.ConnectionType;
import com.nexla.common.CredentialType;
import com.nexla.connector.config.AbstractNoLoggingConfig;
import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.file.RecourceAccessCallbackConfig;
import com.nexla.connector.config.ssh.SshTunnelConfig;
import com.nexla.connector.config.ssh.tunnel.ConfigWithAuth;
import com.nexla.connector.config.vault.AmazonCredentialsStore;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.Getter;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.config.ConfigDef;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestTemplate;

@Getter
public class BaseAuthConfig extends AbstractNoLoggingConfig implements ConfigWithAuth {

  private static final Logger LOGGER = LoggerFactory.getLogger(BaseAuthConfig.class);

  private static final String CRED_SECRET_NAMES = "cred.secrets.names";
  private static final RestTemplate restTemplate = new RestTemplate();
  public static volatile Optional<String> GLOBAL_ENRICHMENT_URL = empty();

  public static final String NX_EXTERNAL_AUTH_TYPE = "nx_external.auth.type";
  public static final String NX_EXTERNAL_AUTH_PROPS = "nx_external.auth.props";
  public static final String RESOURCE_ACCESS_CALLBACK = "resource.access.callback";

  private Integer credsId;
  public final Optional<CredentialType> credentialsType;
  private List<Integer> sshTunnelPorts;

  public final Optional<SshTunnelConfig> sshTunnelConfig;
  public final String customAuthType;
  public final Map<String, String> customAuthProps;
  public final Optional<RecourceAccessCallbackConfig> resourceAccessCallbackConfig;

  public BaseAuthConfig(NexlaConfigDef definition, Map<String, ?> originals, Integer credsId) {
    super(definition, getEnrichedOriginals(originals, credsId));
    this.credsId = opt(originals.get(CREDS_ID)).map(Integer::valueOf).orElse(credsId);
    this.credentialsType =
        opt(originals.get(CREDENTIALS_TYPE))
            .map(String::toUpperCase)
            .map(ConnectionType::fromString);
    this.sshTunnelConfig =
        opt(getString(TUNNEL_BASTION_HOST))
            .map(
                bastionHost -> {
                  String bastionUser = getString(TUNNEL_BASTION_USER);
                  Integer bastionPort = getInt(TUNNEL_BASTION_PORT);
                  return new SshTunnelConfig(bastionUser, bastionHost, bastionPort);
                });

    this.customAuthProps = getCustomAuthProps(originals);
    this.customAuthType = getString(NX_EXTERNAL_AUTH_TYPE);
    this.resourceAccessCallbackConfig = resourceAccessCallbackConfig(originals);
  }

  public BaseAuthConfig(Map<String, ?> originals, Integer credsId) {
    super(baseAuthConfigDef(), getEnrichedOriginals(originals, credsId));
    this.credsId = opt(originals.get(CREDS_ID)).map(Integer::valueOf).orElse(credsId);
    this.credentialsType =
        opt(originals.get(CREDENTIALS_TYPE))
            .map(String::toUpperCase)
            .map(ConnectionType::fromString);
    this.sshTunnelConfig =
        opt(getString(TUNNEL_BASTION_HOST))
            .map(
                bastionHost -> {
                  String bastionUser = getString(TUNNEL_BASTION_USER);
                  Integer bastionPort = getInt(TUNNEL_BASTION_PORT);
                  return new SshTunnelConfig(bastionUser, bastionHost, bastionPort);
                });

    this.customAuthProps = getCustomAuthProps(originals);
    this.customAuthType = getString(NX_EXTERNAL_AUTH_TYPE);
    this.resourceAccessCallbackConfig = resourceAccessCallbackConfig(originals);
  }

  private Optional<RecourceAccessCallbackConfig> resourceAccessCallbackConfig(
      Map<String, ?> originals) {
    Object resourceAccessConfObj = originals.get(RESOURCE_ACCESS_CALLBACK);
    if (resourceAccessConfObj != null) {
      Map<String, Object> resourceAccessConf;
      if (resourceAccessConfObj instanceof Map) {
        resourceAccessConf = (Map) resourceAccessConfObj;
      } else {
        resourceAccessConf = JsonUtils.jsonToMap(resourceAccessConfObj.toString());
      }
      return Optional.of(new RecourceAccessCallbackConfig(resourceAccessConf))
          .filter(
              x -> x.callbackType != RecourceAccessCallbackConfig.RecourceAccessCallbackType.NONE);
    } else {
      return Optional.empty();
    }
  }

  private static Map<String, ?> getEnrichedOriginals(Map<String, ?> originals, Integer credId) {
    try {
      enrichOriginals("", originals);
    } catch (Exception e) {
      LOGGER.error("Credential enrichment error", e);
    }
    try {
      enrichOriginals("nx_external.", originals);
    } catch (Exception e) {
      LOGGER.error("Credential enrichment error", e);
    }
    try {
      enrichWithApi(originals, credId);
    } catch (Exception e) {
      LOGGER.error("Credential enrichment error", e);
    }
    return originals;
  }

  private Map getCustomAuthProps(Map<String, ?> parsedConfig) {
    return (Map)
        ofNullable(parsedConfig.get("nx_external.auth.props"))
            .map(
                x -> {
                  if (x instanceof Map) {
                    return x;
                  } else {
                    return JsonUtils.jsonToMap(x.toString());
                  }
                })
            .orElse(Collections.emptyMap());
  }

  private static Map enrichWithApi(Map originals, Integer credId) {
    if (credId == null || credId == -1) {
      LOGGER.info("Not enriching credential " + credId);
    } else {
      ofNullable(originals.get(CREDENTIAL_ENRICHMENT_URL))
          .map(Object::toString)
          .or(() -> GLOBAL_ENRICHMENT_URL)
          .ifPresent(
              url -> {
                var body =
                    JsonUtils.toJsonString(
                        EntryStream.of(originals)
                            .filterKeys(x -> x.toString().startsWith("nx_external"))
                            .toMap());

                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                LOGGER.info("Enriching credential " + credId + " with API call. Url = " + url);
                String response =
                    restTemplate
                        .exchange(url, POST, new HttpEntity<>(body, headers), String.class)
                        .getBody();
                JsonUtils.jsonToMap(response).forEach(originals::put);
                // LOGGER.info("Enriching credential Response " + credId + " with API call. response
                // = " + response);
              });
    }
    return originals;
  }

  public AWSAuthConfig asAWS() {
    return (AWSAuthConfig) this;
  }

  public static void enrichOriginals(String prefix, Map originals) {
    ofNullable(originals.get(prefix + CRED_SECRET_NAMES))
        .ifPresent(
            secretNames -> {
              String region = stringOrNull(originals.get(prefix + AWS_SECRET_MANAGER_REGION));
              String accessKey =
                  stringOrNull(originals.get(prefix + AWS_SECRET_MANAGER_ACCESS_KEY));
              String secretKey =
                  stringOrNull(originals.get(prefix + AWS_SECRET_MANAGER_SECRET_KEY));
              String roleArn = stringOrNull(originals.get(prefix + AWS_SECRET_MANAGER_ROLE_ARN));
              String identityFile =
                  stringOrNull(originals.get(prefix + AWS_SECRET_MANAGER_IDENTITY_TOKEN_FILE));
              String externalId =
                  stringOrNull(originals.get(prefix + AWS_SECRET_MANAGER_EXTERNAL_ID));

              Map<String, Object> nameMapping =
                  ofNullable(originals.get(prefix + AWS_SECRET_MANAGER_NAME_MAPPING))
                      .map(Object::toString)
                      .map(StringUtils::trimToNull)
                      .map(JsonUtils::jsonToMap)
                      .orElse(Collections.emptyMap());

              AmazonCredentialsStore store =
                  new AmazonCredentialsStore(
                      region, accessKey, secretKey, roleArn, identityFile, externalId);
              StreamEx.of(secretNames.toString().split(","))
                  .forEach(
                      extraSecret ->
                          store
                              .getValuesMap(extraSecret)
                              .ifPresent(
                                  em ->
                                      EntryStream.of(em)
                                          .forKeyValue(
                                              (k, v) -> {
                                                Object key = nameMapping.getOrDefault(k, k);
                                                originals.put(key, v);
                                              })));
            });
  }

  public static String stringOrNull(Object value) {
    return ofNullable(value).map(Object::toString).orElse(null);
  }

  public <T> T withSshTunnelPorts(List<Integer> sshTunnelPort) {
    this.sshTunnelPorts = sshTunnelPort;
    return (T) this;
  }

  public static NexlaConfigDef baseAuthConfigDef() {

    return new NexlaConfigDef()
        .withKey(
            nexlaKey(TUNNEL_BASTION_HOST, ConfigDef.Type.STRING, null)
                .importance(LOW)
                .documentation("SSH tunnel bastion hostname")
                .displayName("SSH tunnel bastion hostname"))
        .withKey(
            nexlaKey(TUNNEL_BASTION_PORT, ConfigDef.Type.INT, 22)
                .importance(LOW)
                .documentation("SSH tunnel bastion port")
                .displayName("SSH tunnel bastion port"))
        .withKey(
            nexlaKey(TUNNEL_BASTION_USER, ConfigDef.Type.STRING, "nexla")
                .importance(LOW)
                .documentation("SSH tunnel bastion user")
                .displayName("Vault token"))
        .withKey(
            nexlaKey(CRED_SECRET_NAMES, ConfigDef.Type.BOOLEAN, false)
                .importance(LOW)
                .documentation("Credential secret names (comma separated)")
                .displayName("Credential secret names (comma separated)"))
        .withKey(
            nexlaKey(AWS_SECRET_MANAGER_REGION, ConfigDef.Type.STRING, null)
                .importance(LOW)
                .documentation("AWS secret manager region")
                .displayName("AWS secret manager region"))
        .withKey(
            nexlaKey(AWS_SECRET_MANAGER_ACCESS_KEY, ConfigDef.Type.STRING, null)
                .importance(LOW)
                .documentation("AWS secret manager access key")
                .displayName("AWS secret manager access key"))
        .withKey(
            nexlaKey(AWS_SECRET_MANAGER_SECRET_KEY, ConfigDef.Type.STRING, null)
                .importance(LOW)
                .documentation("AWS secret manager secret key")
                .displayName("AWS secret manager secret key"))
        .withKey(
            nexlaKey(NX_EXTERNAL_AUTH_TYPE, ConfigDef.Type.STRING, null)
                .documentation("Custom auth type"))
        .withKey(
            nexlaKey(NX_EXTERNAL_AUTH_PROPS, ConfigDef.Type.STRING, "{}")
                .documentation("Custom auth properties (JSON)"));
  }

  @Override
  public BaseAuthConfig authConfig() {
    return this;
  }
}
