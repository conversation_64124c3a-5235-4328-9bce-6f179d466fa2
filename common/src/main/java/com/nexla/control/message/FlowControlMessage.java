package com.nexla.control.message;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.connector.config.FlowType;
import com.nexla.control.ControlMessageFlowAttachment;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Setter;

@Data
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class FlowControlMessage implements ControlMessage {

  private UUID messageId;

  @JsonProperty("resource_id")
  private Integer resourceId;

  @JsonProperty("event_type")
  private ControlEventType eventType;

  @JsonProperty("pipeline_type")
  private FlowType flowType;

  @Setter private String origin;

  @Setter private Map<String, String> context = new HashMap<>();

  @JsonProperty("resource_json")
  @Setter
  private Optional<Map<String, Object>> resourceJson = Optional.empty();

  @JsonProperty("flow")
  private Optional<ControlMessageFlowAttachment>
      flow; // Note. `flow` is not the same as `flow_node`

  private final Long createdAt = System.currentTimeMillis();

  @Override
  public Optional<Boolean> shouldSendToControlTopic() {
    if (eventType == ControlEventType.SERVICE_ON_COMPLETION) {
      return Optional.of(true);
    }
    return ControlMessage.super.shouldSendToControlTopic();
  }

  @Override
  public Optional<Boolean> shouldSendControlUpdateMessage() {
    if (eventType == ControlEventType.SERVICE_ON_COMPLETION) {
      return Optional.of(false);
    }
    return ControlMessage.super.shouldSendControlUpdateMessage();
  }

  @Override
  public ControlResourceType getResourceType() {
    return ControlResourceType.FLOW_NODE;
  }
}
