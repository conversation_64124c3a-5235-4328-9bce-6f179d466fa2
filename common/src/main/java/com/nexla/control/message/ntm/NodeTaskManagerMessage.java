package com.nexla.control.message.ntm;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.nexla.control.message.MessageCreatedAt;
import com.nexla.control.message.ntm.messages.NodeTaskManagerControlMessage;
import com.nexla.control.message.ntm.messages.NodeTaskManagerScaleMessage;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "messageType")
@JsonSubTypes({
  @JsonSubTypes.Type(value = NodeTaskManagerControlMessage.class, name = "CONTROL_MESSAGE"),
  @JsonSubTypes.Type(value = NodeTaskManagerScaleMessage.class, name = "PIPELINE_SCALE_MESSAGE")
})
@JsonIgnoreProperties(ignoreUnknown = true)
public interface NodeTaskManagerMessage extends MessageCreatedAt {

  String getMessageId();

  @JsonIgnore
  NodeTaskManagerMessageType getMessageType();

  default String key() {
    return null;
  }
}
