package com.nexla.control.health;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.Collections;
import java.util.Map;
import lombok.Data;

/**
 * Note1. Do not forget to update expected health events list in ApiHandler::expectedHealthEvents
 * methods Note2. Do not remove old events in order to keep backward compatibility Note3. Do not use
 * the same metricId for recurring health events and for Error health events in order not to have
 * them overriding each other
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ExpectedHealthEvent implements HealthMessage {

  public ExpectedHealthEvent() {
    serviceName = "";
    metricId = "";
    maxMessageIntervalSec = 0L;
  }

  private final String serviceName;
  private final String metricId;
  private final Long maxMessageIntervalSec;
  private Long createdAt;

  private boolean expectedPerService = false;

  public ExpectedHealthEvent(String serviceName, String metricId, Long maxMessageIntervalSec) {
    this(serviceName, metricId, maxMessageIntervalSec, false);
  }

  public ExpectedHealthEvent(
      String serviceName, String metricId, Long maxMessageIntervalSec, boolean expectedPerService) {
    this.serviceName = serviceName;
    this.metricId = metricId;
    this.maxMessageIntervalSec = maxMessageIntervalSec;
    this.expectedPerService = expectedPerService;
  }

  @Override
  public HealthMessageType getMessageType() {
    return HealthMessageType.EXPECTED_EVENT;
  }

  @Override
  public long getTimestamp() {
    return 0;
  }

  @Override
  public String getPodName() {
    return expectedPerService ? "default" : "";
  }

  @Override
  public String getMessage() {
    return "";
  }

  @Override
  public Map<String, String> getContext() {
    return Collections.emptyMap();
  }
}
