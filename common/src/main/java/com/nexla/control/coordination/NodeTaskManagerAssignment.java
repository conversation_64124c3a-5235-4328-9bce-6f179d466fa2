package com.nexla.control.coordination;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class NodeTaskManagerAssignment implements CoordinationMessage {

    private final String messageId;
    private final String nodeId;
    private final Long createdAt = System.currentTimeMillis();

    @Override
    public CoordinationEventType getCoordinationEventType() {
        return CoordinationEventType.NTM_PIPELINE_NODE_ASSIGNMENT;
    }
}