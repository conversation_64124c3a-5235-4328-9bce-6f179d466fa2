package com.nexla.control.coordination;

import com.nexla.common.listing.ListingUpdateSetFileStatusTask;
import com.nexla.control.ListingFileStatus;
import java.util.Optional;
import lombok.Data;

@Data
@Deprecated
public class SetFileStatusCoordination implements CoordinationMessage {

  public final String messageId;
  public final Long sourceId;
  public final Long fileId;
  public final ListingFileStatus status;
  public final Long lastMessageOffset;
  public final String message;
  public final Long ttl;
  private final Long createdAt = System.currentTimeMillis();

  @Override
  public CoordinationEventType getCoordinationEventType() {
    return CoordinationEventType.SET_FILE_STATUS;
  }

  public ListingUpdateSetFileStatusTask toUpdateListingSetFileStatusTask() {
    return new ListingUpdateSetFileStatusTask(
        messageId,
        fileId,
        status.toString(),
        Optional.ofNullable(lastMessageOffset),
        Optional.empty(),
        Optional.ofNullable(message),
        Optional.ofNullable(sourceId),
        ttl);
  }
}
