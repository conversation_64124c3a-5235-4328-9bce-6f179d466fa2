package com.nexla.common;

import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toMap;
import static org.apache.commons.lang3.StringUtils.trimToNull;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import one.util.streamex.StreamEx;
import org.apache.commons.lang3.StringUtils;

public class ConfigUtils {

  public static String env(String key) {
    return System.getenv(key.toUpperCase().replaceAll("[.|-]", "_"));
  }

  public static String withMacro(String value, Function<String, String> macroResolver) {
    return opt(value).map(macroResolver).flatMap(a -> ofNullable(trimToNull(a))).orElse(null);
  }

  public static Optional<String> opt(Object value) {
    return ofNullable(value).map(Object::toString).map(StringUtils::trimToNull);
  }

  public static Optional<String> opt(String value) {
    return ofNullable(trimToNull(value));
  }

  public static Optional<String> optWithMacro(
      String value, Function<String, String> macroResolver) {
    return opt(value).map(macroResolver).flatMap(a -> ofNullable(trimToNull(a)));
  }

  public static Optional<Boolean> optBoolean(Object value) {
    return ofNullable(value)
        .map(Object::toString)
        .map(StringUtils::trimToNull)
        .map(Boolean::parseBoolean);
  }

  public static Optional<Boolean> optBooleanWithMacro(
      Object value, Function<String, String> macroResolver) {
    return ofNullable(value)
        .map(Object::toString)
        .flatMap(a -> ofNullable(trimToNull(a)))
        .map(macroResolver)
        .flatMap(a -> ofNullable(trimToNull(a)))
        .map(Boolean::parseBoolean);
  }

  public static Optional<Integer> optInt(Object value) {
    return ofNullable(value)
        .map(Object::toString)
        .flatMap(a -> ofNullable(trimToNull(a)))
        .map(Integer::valueOf);
  }

  public static Optional<Integer> optIntWithMacro(
      Object value, Function<String, String> macroResolver) {
    return ofNullable(value)
        .map(Object::toString)
        .flatMap(a -> ofNullable(trimToNull(a)))
        .map(macroResolver)
        .flatMap(a -> ofNullable(trimToNull(a)))
        .map(Integer::valueOf);
  }

  public static Optional<Long> optLong(Object value) {
    return ofNullable(value)
        .map(Object::toString)
        .flatMap(a -> ofNullable(trimToNull(a)))
        .map(Long::valueOf);
  }

  public static Optional<List<String>> optListWithMacro(
      Object value, Function<String, String> macroResolver) {
    Object stringValue = value;
    if (value instanceof List) // support for simple list past directly
    stringValue =
          ((List<?>) value)
              .stream()
                  .map(Object::toString)
                  .map(ConfigUtils::trimQuotes)
                  .map(macroResolver)
                  .collect(Collectors.joining(","));
    return ofNullable(stringValue)
        .map(Object::toString)
        .flatMap(a -> ofNullable(trimToNull(a)))
        .map(macroResolver)
        .flatMap(a -> ofNullable(trimToNull(a)))
        .map(x -> parseListWithMacro(x, macroResolver));
  }

  public static Map<String, String> parseParametersToMap(List<String> parseParametersMap) {
    return parseParametersMap.stream()
        .filter(StringUtils::isNotEmpty)
        .collect(
            toMap(
                value -> value.split(":")[0],
                value -> StreamEx.of(value.split(":")).skip(1).map(String::trim).joining(":")));
  }

  private static List<String> parseListWithMacro(
      String value, Function<String, String> macroResolver) {
    String rawValues = StringUtils.removeEnd(StringUtils.removeStart(value, "["), "]");
    return Arrays.asList(rawValues.split("\\s*,\\s*", -1)).stream()
        .map(ConfigUtils::trimQuotes)
        .map(macroResolver)
        .collect(Collectors.toList());
  }

  private static String trimQuotes(String value) {
    return StringUtils.removeEnd(StringUtils.removeStart(value, "\""), "\"");
  }
}
