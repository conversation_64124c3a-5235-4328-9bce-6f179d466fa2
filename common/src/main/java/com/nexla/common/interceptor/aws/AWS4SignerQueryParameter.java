package com.nexla.common.interceptor.aws;

import com.nexla.common.interceptor.aws.util.AwsBinaryUtils;
import java.net.URL;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * AWS4 signer implementation which uses query parameters. Typical use case is to create a
 * pre-signed url. Creates signature for requests to Amazon services and places it into a String to
 * be included in the request.
 */
public class AWS4SignerQueryParameter extends AWS4Signer {

  public AWS4SignerQueryParameter(
      URL endpointUrl, String httpMethod, String serviceName, String regionName) {
    super(endpointUrl, httpMethod, serviceName, regionName);
  }

  /**
   * Computes an AWS4 authorization for a request, suitable for embedding in query parameters.
   *
   * @param headers The request headers; 'Host' will be added to this set.
   * @param queryParameters Any query parameters that will be added to the endpoint. The parameters
   *     should be specified in canonical format.
   * @param bodyHash Precomputed SHA256 hash of the request body content; this value should also be
   *     set as the header 'X-Amz-Content-SHA256' for non-streaming uploads.
   * @param awsAccessKey The user's AWS Access Key.
   * @param awsSecretKey The user's AWS Secret Key.
   * @return The computed authorization as a Map for the request. All these values from the map
   *     should be set as the query parameters on the subsequent HTTP request.
   */
  public Map<String, String> computeSignature(
      Map<String, String> headers,
      Map<String, String> queryParameters,
      String bodyHash,
      String awsAccessKey,
      String awsSecretKey) {
    LinkedHashMap<String, String> queryParametersResult = new LinkedHashMap<>();
    Date now = new Date();
    String dateTimeStamp = dateTimeFormat.format(now);
    headers.putIfAbsent("Host", endpointUrl.getHost());

    String canonicalizedHeaderNames = getCanonicalizeHeaderNames(headers);
    String canonicalizedHeaders = getCanonicalizedHeaderString(headers);
    String dateStamp = dateStampFormat.format(now);
    String scope = dateStamp + "/" + regionName + "/" + serviceName + "/" + TERMINATOR;
    queryParameters.put("X-Amz-Algorithm", SCHEME + "-" + ALGORITHM);
    queryParameters.put("X-Amz-Credential", awsAccessKey + "/" + scope);
    queryParameters.put("X-Amz-Date", dateTimeStamp);
    queryParameters.put("X-Amz-SignedHeaders", canonicalizedHeaderNames);

    String canonicalizedQueryParameters = getCanonicalizedQueryString(queryParameters);
    String canonicalRequest =
        getCanonicalRequest(
            endpointUrl,
            httpMethod,
            canonicalizedQueryParameters,
            canonicalizedHeaderNames,
            canonicalizedHeaders,
            bodyHash);

    String stringToSign =
        getStringToSign(SCHEME, ALGORITHM, dateTimeStamp, scope, canonicalRequest);

    byte[] kSecret = (SCHEME + awsSecretKey).getBytes();
    byte[] kDate = sign(dateStamp, kSecret, "HmacSHA256");
    byte[] kRegion = sign(regionName, kDate, "HmacSHA256");
    byte[] kService = sign(serviceName, kRegion, "HmacSHA256");
    byte[] kSigning = sign(TERMINATOR, kService, "HmacSHA256");
    byte[] signature = sign(stringToSign, kSigning, "HmacSHA256");

    queryParametersResult.put("X-Amz-Algorithm", queryParameters.get("X-Amz-Algorithm"));
    queryParametersResult.put("X-Amz-Credential", queryParameters.get("X-Amz-Credential"));
    queryParametersResult.put("X-Amz-Date", queryParameters.get("X-Amz-Date"));
    queryParametersResult.put("X-Amz-Expires", queryParameters.get("X-Amz-Expires"));
    queryParametersResult.put("X-Amz-SignedHeaders", queryParameters.get("X-Amz-SignedHeaders"));
    queryParametersResult.put("X-Amz-Signature", AwsBinaryUtils.toHex(signature));
    return queryParametersResult;
  }
}
