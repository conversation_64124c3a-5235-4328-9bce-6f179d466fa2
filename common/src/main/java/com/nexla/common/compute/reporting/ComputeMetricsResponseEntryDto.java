package com.nexla.common.compute.reporting;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.ImmutableList;
import com.nexla.common.compute.ComputeMetricEntry;
import com.nexla.common.compute.ComputeMetricSource;
import java.time.LocalDate;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.With;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ComputeMetricsResponseEntryDto {
  @JsonProperty("resource_id")
  private final Integer resourceId;

  @JsonProperty("resource_type")
  private final ComputeMetricSource resourceType;

  @JsonProperty("run_id")
  private final Long runId;

  @JsonProperty("reporting_date")
  private final LocalDate reportingDate;

  @JsonProperty("flow_node_id")
  private final Integer flowNodeId;

  @JsonProperty("origin_node_id")
  private final Integer originNodeId;

  @JsonProperty("org_id")
  private final Integer orgId;

  @JsonProperty("owner_id")
  private final Integer ownerId;

  @JsonProperty("metrics")
  @With
  @Builder.Default
  private final List<ComputeMetricEntry> metrics = ImmutableList.of();
}
