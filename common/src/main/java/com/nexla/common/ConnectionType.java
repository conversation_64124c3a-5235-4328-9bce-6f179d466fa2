package com.nexla.common;

import static com.nexla.common.NexlaConstants.ConnectionTypeCategory.*;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.nexla.common.NexlaConstants.ConnectionTypeCategory;
import com.nexla.common.connectiontype.ConnectionTypeBuilder;
import com.nexla.common.connectiontype.LoadConnectorServiceClient;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.Data;

@Data
public class ConnectionType implements CredentialType {

  public static final Map<String, ConnectionType> CONNECTION_TYPE_MAP = Maps.newHashMap();

  public final String name;
  public final ConnectionTypeCategory category;
  public final String connectionStringPrefix;

  public final boolean isWarehouseSource;
  public final boolean isWarehouseSink;
  public final boolean isSourceConnectorType;

  public final boolean isRefreshable;

  private static ConnectionTypeBuilder ct(String name, ConnectionTypeCategory category) {
    return new ConnectionTypeBuilder(name, category);
  }

  @JsonValue
  public String name() {
    return name;
  }

  @JsonCreator
  public static ConnectionType fromString(String key) {
    ConnectionType result =
        CONNECTION_TYPE_MAP.computeIfAbsent(
            key.toUpperCase(),
            s -> LoadConnectorServiceClient.getRemoteCredentialTypes().get(key.toUpperCase()));

    if (result == null) {
      return UNKNOWN;
    } else {
      return result;
    }
  }

  public static ConnectionType validateString(String key) {
    ConnectionType result =
        CONNECTION_TYPE_MAP.computeIfAbsent(
            key.toUpperCase(),
            s -> LoadConnectorServiceClient.getRemoteCredentialTypes().get(key.toUpperCase()));

    if (result == null) {
      throw new IllegalArgumentException("Unknown ConnectionType: " + key);
    }
    return result;
  }

  public <E extends ConnectionType> boolean amongTypes(E... elements) {
    return Sets.newHashSet(elements).stream()
        .map(ConnectionType::name)
        .anyMatch(x -> Objects.equals(x, this.name));
  }

  public boolean isDatabase() {
    return category == DATABASE;
  }

  public boolean isFile() {
    return category == FILE;
  }

  public boolean isDocumentDB() {
    return category == DOCUMENT_DB;
  }

  public boolean isVectorDB() {
    return category == VECTOR_DB;
  }

  public boolean isRest() {
    return amongTypes(REST, SOAP);
  }

  public boolean isStreaming() {
    return amongTypes(JMS, GOOGLE_PUBSUB) || isKafka();
  }

  public boolean isKafka() {
    return amongTypes(KAFKA, CONFLUENT_KAFKA);
  }

  public boolean isSupportingSchema() {
    return !amongTypes(NETSUITE_JDBC, ORACLE_AUTONOMOUS, SYBASE);
  }

  @Override
  public Optional<ConnectionType> asConnectionType() {
    return Optional.of(this);
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    ConnectionType that = (ConnectionType) o;
    return Objects.equals(name, that.name);
  }

  @Override
  public int hashCode() {
    return Objects.hash(name);
  }

  @Override
  public String toString() {
    return name.toUpperCase();
  }

  public static ConnectionType FTP = ct("FTP", FILE).build();
  public static ConnectionType S3 = ct("S3", FILE).build();
  public static ConnectionType GDRIVE = ct("GDRIVE", FILE).isRefreshable().build();
  public static ConnectionType GCS = ct("GCS", FILE).isRefreshable().build();
  public static ConnectionType AZURE_BLB = ct("AZURE_BLB", FILE).build();
  public static ConnectionType AZURE_DATA_LAKE = ct("AZURE_DATA_LAKE", FILE).build();
  public static ConnectionType DATABRICKS_AWS = ct("DATABRICKS_AWS", null).build();
  public static ConnectionType DATABRICKS_GCP = ct("DATABRICKS_GCP", null).build();
  public static ConnectionType DATABRICKS_AZURE = ct("DATABRICKS_AZURE", null).build();
  public static ConnectionType DELTA_LAKE_S3 = ct("DELTA_LAKE_S3", FILE).build();
  public static ConnectionType DELTA_LAKE_AZURE_BLB = ct("DELTA_LAKE_AZURE_BLB", FILE).build();
  public static ConnectionType DELTA_LAKE_AZURE_DATA_LAKE =
      ct("DELTA_LAKE_AZURE_DATA_LAKE", FILE).build();
  public static ConnectionType BIGQUERY = ct("BIGQUERY", DATABASE).isRefreshable().build();
  public static ConnectionType JMS = ct("JMS", null).build();
  public static ConnectionType KAFKA = ct("KAFKA", null).build();
  public static ConnectionType CONFLUENT_KAFKA = ct("CONFLUENT_KAFKA", null).build();
  public static ConnectionType GOOGLE_PUBSUB = ct("GOOGLE_PUBSUB", null).build();
  public static ConnectionType MONGO = ct("MONGO", DOCUMENT_DB).build();
  public static ConnectionType FIREBASE = ct("FIREBASE", DOCUMENT_DB).build();
  public static ConnectionType DYNAMODB = ct("DYNAMODB", DOCUMENT_DB).build();
  public static ConnectionType KINESIS = ct("KINESIS", null).build();
  public static ConnectionType GITHUB = ct("GITHUB", null).build();
  public static ConnectionType BOX = ct("BOX", FILE).isRefreshable().build();
  public static ConnectionType DROPBOX = ct("DROPBOX", FILE).isRefreshable().build();
  public static ConnectionType WEBDAV = ct("WEBDAV", FILE).build();
  public static ConnectionType MYSQL =
      new ConnectionTypeBuilder("MYSQL", DATABASE).withConnectionPrefix("jdbc:mysql://").build();

  public static ConnectionType ORACLE =
      new ConnectionTypeBuilder("ORACLE", DATABASE)
          .withConnectionPrefix("jdbc:oracle:thin:@//")
          .build();
  public static ConnectionType REDSHIFT =
      new ConnectionTypeBuilder("REDSHIFT", DATABASE)
          .withConnectionPrefix("jdbc:redshift://")
          .warehouseSource()
          .warehouseSink()
          .build();
  public static ConnectionType POSTGRES =
      new ConnectionTypeBuilder("POSTGRES", DATABASE)
          .withConnectionPrefix("jdbc:postgresql://")
          .build();
  public static ConnectionType SNOWFLAKE =
      new ConnectionTypeBuilder("SNOWFLAKE", DATABASE)
          .withConnectionPrefix("jdbc:snowflake://")
          .warehouseSource()
          .warehouseSink()
          .build();
  public static ConnectionType SQLSERVER =
      new ConnectionTypeBuilder("SQLSERVER", DATABASE)
          .withConnectionPrefix("jdbc:sqlserver://")
          .build();
  public static ConnectionType SQLITE =
      new ConnectionTypeBuilder("SQLITE", DATABASE).withConnectionPrefix("jdbc:sqlite://").build();
  public static ConnectionType HANA_JDBC =
      new ConnectionTypeBuilder("HANA_JDBC", DATABASE).withConnectionPrefix("jdbc:sap://").build();
  public static ConnectionType HIVE =
      new ConnectionTypeBuilder("HIVE", DATABASE).withConnectionPrefix("jdbc:hive2://").build();
  public static ConnectionType PRESTO =
      new ConnectionTypeBuilder("PRESTO", DATABASE).withConnectionPrefix("jdbc:presto://").build();
  public static ConnectionType AS400 =
      new ConnectionTypeBuilder("AS400", DATABASE).withConnectionPrefix("jdbc:as400://").build();
  public static ConnectionType FIREBOLT =
      new ConnectionTypeBuilder("FIREBOLT", DATABASE)
          .withConnectionPrefix("jdbc:firebolt://")
          .warehouseSink()
          .build();

  public static ConnectionType GCP_SPANNER =
      new ConnectionTypeBuilder("GCP_SPANNER", DATABASE)
          .withConnectionPrefix("jdbc:cloudspanner://")
          .build();
  public static ConnectionType DB2 =
      new ConnectionTypeBuilder("DB2", DATABASE).withConnectionPrefix("jdbc:db2://").build();
  public static ConnectionType AWS_ATHENA =
      new ConnectionTypeBuilder("AWS_ATHENA", DATABASE)
          .withConnectionPrefix("jdbc:awsathena://")
          .build();

  public static ConnectionType REST = ct("REST", API).isRefreshable().build();

  public static ConnectionType SOAP = ct("SOAP", API).build();

  // for demo
  public static ConnectionType UNKNOWN = ct("UNKNOWN", null).build();
  public static ConnectionType API_PULL = ct("API_PULL", API).noSourceContainer().build();
  public static ConnectionType API_PUSH = ct("API_PUSH", API).noSourceContainer().build();
  public static ConnectionType NEXLA_REST = ct("NEXLA_REST", API).noSourceContainer().build();
  public static ConnectionType DATA_MAP = ct("DATA_MAP", ConnectionTypeCategory.REDIS).build();
  public static ConnectionType FILE_UPLOAD = ct("FILE_UPLOAD", FILE).build();
  public static ConnectionType ORACLE_AUTONOMOUS =
      new ConnectionTypeBuilder("ORACLE_AUTONOMOUS", DATABASE)
          .withConnectionPrefix("******************************=")
          .warehouseSource()
          .warehouseSink()
          .build();
  public static ConnectionType AZURE_SYNAPSE =
      new ConnectionTypeBuilder("AZURE_SYNAPSE", DATABASE)
          .withConnectionPrefix("jdbc:sqlserver://")
          .warehouseSink()
          .build();

  // JPMC Internal S3 Storage
  public static ConnectionType MERCURY_S3 = ct("MERCURY_S3", FILE).build();
  public static ConnectionType TERADATA =
      new ConnectionTypeBuilder("TERADATA", DATABASE)
          .withConnectionPrefix("jdbc:teradata://")
          .build();
  public static ConnectionType NEPTUNE_S3 = ct("NEPTUNE_S3", FILE).build();
  public static ConnectionType MIN_IO_S3 = ct("MIN_IO_S3", FILE).build();
  public static ConnectionType DATABRICKS =
      new ConnectionTypeBuilder("DATABRICKS", DATABASE)
          .withConnectionPrefix("jdbc:databricks://")
          .warehouseSink()
          .build();
  public static ConnectionType NETSUITE_JDBC =
      new ConnectionTypeBuilder("NETSUITE_JDBC", DATABASE)
          .withConnectionPrefix("jdbc:ns://")
          .build();
  public static ConnectionType SYBASE =
      new ConnectionTypeBuilder("SYBASE", DATABASE)
          .withConnectionPrefix("jdbc:sybase:Tds:")
          .build();
  public static ConnectionType ONEDRIVE = ct("ONEDRIVE", FILE).isRefreshable().build();
  public static ConnectionType SHAREPOINT = ct("SHAREPOINT", FILE).isRefreshable().build();

  public static ConnectionType API_STREAMS = ct("API_STREAMS", API).isRefreshable().build();

  public static ConnectionType API_MULTI = ct("API_MULTI", API).isRefreshable().build();

  public static ConnectionType PINECONE = ct("PINECONE", VECTOR_DB).build();

  public static ConnectionType S3_ICEBERG = ct("S3_ICEBERG", null).build();
}
