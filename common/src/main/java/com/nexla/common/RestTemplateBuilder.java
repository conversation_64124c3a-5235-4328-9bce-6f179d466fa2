package com.nexla.common;

import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.client.RestTemplate;

public class RestTemplateBuilder {
  private static final Logger LOGGER = LoggerFactory.getLogger(RestTemplateBuilder.class);

  private NexlaSslContext ssl;
  private boolean bufferRequestBody = true;
  private boolean logVerbose = false;

  public RestTemplateBuilder withSSL(NexlaSslContext ssl) {
    this.ssl = ssl;
    return this;
  }

  public RestTemplateBuilder withLogVerbose(boolean logVerbose) {
    this.logVerbose = logVerbose;
    return this;
  }

  public RestTemplateBuilder withBufferRequestBody(boolean bufferRequestBody) {
    this.bufferRequestBody = bufferRequestBody;
    return this;
  }

  public RestTemplate build() {
    return optPem()
        .or(this::optP12)
        .orElseGet(() -> AppUtils.defaultRestTemplate(bufferRequestBody, logVerbose));
  }

  private Optional<RestTemplate> optPem() {
    return Optional.ofNullable(ssl)
        .flatMap(NexlaSslContext::getClientPemCert)
        .map(pem -> AppUtils.nexlaRestTemplate(pem, bufferRequestBody, logVerbose));
  }

  private Optional<RestTemplate> optP12() {
    return Optional.ofNullable(ssl)
        .flatMap(NexlaSslContext::getClientKeystoreStore)
        .map(
            cert ->
                AppUtils.nexlaRestTemplate(
                    cert,
                    ssl.getClientTruststoreStore().orElse(null),
                    bufferRequestBody,
                    logVerbose));
  }
}
