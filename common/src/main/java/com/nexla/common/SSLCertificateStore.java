package com.nexla.common;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.util.Base64;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import lombok.Getter;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;

@Getter
public class SSLCertificateStore {
  private final boolean deleteOnClean;
  private final File file;
  private final String password;

  private static final Map<String, File> fileMap = new ConcurrentHashMap<>();

  public SSLCertificateStore(File file, String password) {
    this.file = file;
    this.password = password;

    this.deleteOnClean = false;
  }

  @SneakyThrows
  public SSLCertificateStore(String p12, String password) {
    this.file = fileMap.computeIfAbsent(p12, k -> saveFile(p12));
    this.password = password;

    this.deleteOnClean = true;
  }

  @SneakyThrows
  private File saveFile(String p12) {
    byte[] binary = Base64.getDecoder().decode(p12);
    File local = File.createTempFile(UUID.randomUUID().toString(), "");
    try (FileOutputStream fos = new FileOutputStream(local)) {
      IOUtils.write(binary, fos);
    }
    return local;
  }

  public void clean() {
    if (file.exists() && deleteOnClean) {
      file.delete();

      fileMap.values().removeIf(f -> f.getAbsolutePath().equals(file.getAbsolutePath()));
    }
  }

  public KeyStore pkcs12()
      throws KeyStoreException, IOException, CertificateException, NoSuchAlgorithmException {
    return fetch("PKCS12");
  }

  public KeyStore jks()
      throws KeyStoreException, IOException, CertificateException, NoSuchAlgorithmException {
    return fetch("JKS");
  }

  public KeyStore fetch(String type)
      throws KeyStoreException, IOException, CertificateException, NoSuchAlgorithmException {
    KeyStore s = KeyStore.getInstance(type);
    try (FileInputStream stream = new FileInputStream(this.getFile())) {
      s.load(stream, this.getPassword().toCharArray());
    }
    return s;
  }

  @Override
  public String toString() {
    return "SSLCertificateStore{ file=" + file + " }";
  }

  public String toKeyString() {
    return "SSLCertificateStore{" + "file=" + file + ", password='" + password + '\'' + '}';
  }
}
