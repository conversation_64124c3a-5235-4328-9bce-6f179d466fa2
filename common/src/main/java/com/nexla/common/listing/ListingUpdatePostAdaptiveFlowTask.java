package com.nexla.common.listing;

import com.nexla.common.ResourceType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ListingUpdatePostAdaptiveFlowTask implements ListingUpdateMessage {
  @Override
  public ListingUpdateTaskType getTaskType() {
    return ListingUpdateTaskType.LISTING_UPDATE_POST_ADAPTIVE_FLOW_TASK;
  }

  private String messageId;

  private ResourceType resourceType;
  private Integer resourceId;
  private Integer flowId;
  private Integer orgId;
  private String status;
  private String parameters;
  private final Long createdAt = System.currentTimeMillis();

  @Override
  public String key() {
    return resourceId.toString();
  }
}
