package com.nexla.common.shutdown;

import static org.junit.Assert.assertEquals;

import com.nexla.test.UnitTests;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.experimental.categories.Category;

@Slf4j
@Category(UnitTests.class)
public class TaskLifecycleManagerTest {

  @Test
  public void taskLifecycleManagerTest() {
    AtomicInteger value = new AtomicInteger(0);

    CompletableFuture.runAsync(
        () ->
            TaskLifecycleManager.submit(
                () -> {
                  Thread.sleep(600);
                  value.compareAndSet(10, 15);
                  return new Integer(value.get());
                }));
    CompletableFuture.runAsync(
        () ->
            TaskLifecycleManager.submit(
                () -> {
                  Thread.sleep(400);
                  value.compareAndSet(5, 10);
                  return new Integer(value.get());
                }));
    CompletableFuture.runAsync(
        () ->
            TaskLifecycleManager.submit(
                () -> {
                  Thread.sleep(200);
                  value.compareAndSet(0, 5);
                  return new Integer(value.get());
                }));

    TaskLifecycleManager.createExecutorServiceShutdownHook(TaskLifecycleManager.executorService, 5)
        .run();
    assertEquals(15, value.get());
  }
}
