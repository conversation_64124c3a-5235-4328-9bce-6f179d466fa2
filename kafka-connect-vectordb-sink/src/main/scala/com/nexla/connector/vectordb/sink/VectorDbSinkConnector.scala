package com.nexla.connector.vectordb.sink

import com.nexla.connect.common.connector.BaseSinkConnector
import org.apache.kafka.common.config.ConfigDef
import org.apache.kafka.connect.connector.Task
import com.nexla.connector.config.vectordb.VectorSinkConnectorConfig;
import VectorDbSinkConnector.VECTORDB_SINK_TELEMETRY_NAME

class VectorDbSinkConnector extends BaseSinkConnector {

  override def telemetryAppName = VECTORDB_SINK_TELEMETRY_NAME

  override def taskClass(): Class[_ <: Task] = classOf[VectorDbSinkTask]

  override def config(): ConfigDef = VectorSinkConnectorConfig.configDef

  override def stop(): Unit = {}
}

object VectorDbSinkConnector {
  val VECTORDB_SINK_TELEMETRY_NAME = "vectordb-sink"
}
