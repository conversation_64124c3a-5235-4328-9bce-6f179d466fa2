package com.nexla.web.service;

public interface Tags {

  static String tag(String tag, Object value) {
    return tag + TAG_SEPARATOR + value;
  }

  String TAG_SEPARATOR = ":";

  String TAG_ENV = "environment";

  String CLIENT_TAG_KEY = "script_name";
  String CLIENT_TAG_VAL = "backend";

  String HOST_KEY = "script_location";
  String TAG_RESOURCE_ID = "resource_id";
  String TAG_RESOURCE_NAME = "resource_name";
  String TAG_RESOURCE_OWNER_ID = "resource_owner_id";
  String TAG_RESOURCE_OWNER_NAME = "resource_owner_name";
  String TAG_RESOURCE_TYPE = "resource_type";
  String TAG_STATUS = "status";
  String TAG_ORG_ID = "org_id";
  String TAG_ORG_NAME = "org_name";
  String TAG_CONNECTION_TYPE = "connection_type";
  String TAG_RESOURCE_FORMAT = "resource_format";
  String TAG_RESOURCE_CHANNEL = "resource_channel";
  String TAG_RESOURCE_ENDPOINT = "resource_endpoint";

  String TAG_CONNECTOR_STATE = "connector_state";
  String TAG_CONNECTOR_TASK_STATE = "connector_task_state";
}
