package com.nexla.connect.common.postponedFlush;

import com.bazaarvoice.jolt.JsonUtils;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.flownode.FlowNodeDatasource;
import com.nexla.client.JavaJobSchedulerClient;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.kafka.service.KafkaLagCalculator;
import one.util.streamex.EntryStream;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.nexla.common.NexlaNamingUtils.sourceConnectorServiceName;

public class KafkaProgressTracker implements PipelineProgressTracker {
    private final KafkaLagCalculator kafkaLagCalculator;
    private final AdminApiClient adminApiClient;
    private final Optional<JavaJobSchedulerClient> ctrlClient;
    private final NexlaLogger logger;

    public final boolean isInmemoryPipeline() { return false; }

    public KafkaProgressTracker(KafkaLagCalculator kafkaLagCalculator, AdminApiClient adminApiClient, Optional<JavaJobSchedulerClient> ctrlClient, NexlaLogger logger) {
        this.kafkaLagCalculator = kafkaLagCalculator;
        this.adminApiClient = adminApiClient;
        this.ctrlClient = ctrlClient;
        this.logger = logger;
    }

    public boolean noMoreRecordsToRead(int sinkId) {
        try {
            List<Integer> dataSets = adminApiClient.getDataSetsBySink(sinkId);
            Map<String, Long> lags = kafkaLagCalculator.getPipelineLag(sinkId, dataSets);
            long pipelineLag = EntryStream.of(lags)
                    .values()
                    .mapToLong(x -> x)
                    .sum();

            if (pipelineLag == 0) {
                logger.info("Checking lag for {} : there is no lag", sinkId);
            } else {
                logger.info("Checking lag for {} : {}", sinkId, JsonUtils.toJsonString(lags));
            }
            return pipelineLag == 0;
        } catch (Exception e) {
            logger.error("Failed to check if there is lag for sink: {}", sinkId, e);
            return false;
        }
    }

    public boolean isSourceFinished(FlowNodeDatasource dataSource) {
        String sourceName = sourceConnectorServiceName(dataSource.getId(), dataSource.getConnectionType());
        return ctrlClient
                .filter(x -> dataSource.getConnectionType().isSourceConnectorType())
                .map(ctrlClient -> {
                    try {
                        boolean exist = ctrlClient.isServiceRunning(sourceName);
                        return !exist;
                    } catch (Exception e) {
                        logger.error("Failed to check if source {} exists", sourceName, e);
                        return false;
                    }
                })
                .orElse(false);
    }
}


