package com.nexla.parser.orc;

import one.util.streamex.StreamEx;
import org.apache.orc.TypeDescription;
import org.everit.json.schema.ArraySchema;
import org.everit.json.schema.BooleanSchema;
import org.everit.json.schema.CombinedSchema;
import org.everit.json.schema.NumberSchema;
import org.everit.json.schema.ObjectSchema;
import org.everit.json.schema.Schema;
import org.everit.json.schema.StringSchema;
import org.everit.json.schema.internal.DateTimeFormatValidator;

import java.util.List;

public class OrcSchemaUtils {

	public static Schema getOrcSchema(TypeDescription orcSchema) {

		TypeDescription.Category category = orcSchema.getCategory();
		switch (category) {
			case STRUCT:
				ObjectSchema.Builder root = ObjectSchema.builder();

				StreamEx.of(orcSchema.getFieldNames())
					.zipWith(StreamEx.of(orcSchema.getChildren()))
					.forKeyValue((fieldName, definition) ->
						root.addPropertySchema(fieldName, getOrcSchema(definition)));

				return root.build();

			case LONG:
				return NumberSchema.builder().requiresInteger(true).build();
			case INT:
				return NumberSchema.builder().requiresInteger(true).build();
			case BYTE:
				return NumberSchema.builder().requiresInteger(true).build();
			case SHORT:
				return NumberSchema.builder().requiresInteger(true).build();
			case FLOAT:
				return NumberSchema.builder().build();
			case DOUBLE:
				return NumberSchema.builder().build();
			case DECIMAL:
				return NumberSchema.builder().build();
			case BOOLEAN:
				return BooleanSchema.builder().build();
			case CHAR:
				return StringSchema.builder().build();
			case STRING:
				return StringSchema.builder().build();
			case DATE:
				return StringSchema.builder().formatValidator(new DateTimeFormatValidator()).build();
			case TIMESTAMP:
				return StringSchema.builder().formatValidator(new DateTimeFormatValidator()).build();
			case VARCHAR:
				return StringSchema.builder().build();
			case BINARY:
				return StringSchema.builder().build();
			case LIST: {
				TypeDescription itemType = orcSchema.getChildren().get(0);
				Schema itemSchema = getOrcSchema(itemType);
				return ArraySchema.builder().allItemSchema(itemSchema).build();
			}
			case MAP: {
				List<Schema> childrenSchemas = StreamEx.of(orcSchema.getChildren()).map(OrcSchemaUtils::getOrcSchema).toList();
				Schema keyType = childrenSchemas.get(0);
				Schema valueType = childrenSchemas.get(1);

				ObjectSchema itemSchema = ObjectSchema.builder()
					.addPropertySchema(PrintData._KEY, keyType)
					.addPropertySchema(PrintData._VALUE, valueType)
					.build();

				return ArraySchema.builder().allItemSchema(itemSchema).build();
			}
			case UNION:
				return CombinedSchema
					.oneOf(
						StreamEx
							.of(orcSchema.getChildren())
							.map(OrcSchemaUtils::getOrcSchema)
							.toList())
					.build();

			default:
				throw new IllegalArgumentException("Unknown field type: " + category);

		}
	}
}
