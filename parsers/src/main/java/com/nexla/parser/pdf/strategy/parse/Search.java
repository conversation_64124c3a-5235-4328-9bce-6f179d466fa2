package com.nexla.parser.pdf.strategy.parse;

import com.nexla.parser.pdf.strategy.parse.geom.Rect;
import com.nexla.parser.pdf.strategy.parse.util.TextPositions;
import lombok.Data;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.graphics.state.RenderingMode;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.text.TextPosition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.*;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class Search {
    private static final Logger LOGGER = LoggerFactory.getLogger(Search.class);

    private final PDDocument document;
    private final List<Integer> pages;

    public Search(PDDocument document, List<Integer> pages) {
        this.document = document;
        this.pages = pages;
    }

    @SneakyThrows
    public List<Match> search(final List<String> terms) {
        List<Match> matches = new ArrayList<>();

        PDFTextStripper stripper = new PDFTextStripper() {
            @Override
            protected void writeString(String text, List<TextPosition> textPositions) {
                if (!pages.isEmpty() && !pages.contains(getCurrentPageNo() - 1)) { // getCurrentPageNo() is 1-based
                    return;
                }

                if (StringUtils.isBlank(text)) {
                    return;
                }

                // yes, it is possible for a single text to contain multiple terms:/
                List<String> occurrences = terms.stream().filter(text::contains).collect(Collectors.toList());
                for (String occurrence : occurrences) {
                    if (occurrence != null) {
                        int startAt = 0;
                        if (textPositions.size() != occurrence.length()) {
                            String t = textPositions.stream()
                                    .map(TextPosition::getUnicode)
                                    .collect(Collectors.joining());

                            startAt = t.indexOf(occurrence);
                            if (startAt == -1) { // should never happen, but just in case
                                LOGGER.warn("Could not find occurrence " + occurrence + " in " + t);
                                return;
                            }
                        }

                        double widthOfSpace = textPositions.get(0).getWidthOfSpace();

                        Rectangle r = null;
                        boolean isBold = false;
                        for (int i = startAt; i < occurrence.length() + startAt; i++) {
                            TextPosition textPosition = textPositions.get(i);
                            Rectangle r1 = TextPositions.translate(textPosition, getCurrentPage().getCropBox());

                            isBold = isBold || isBold(textPosition);
                            r = r == null ? r1 : Rect.merge(r, r1);
                        }

                        if (r != null) {
                            matches.add(new Match(occurrence, r, this.getCurrentPageNo() - 1, isBold, widthOfSpace));
                        }
                    }
                }
            }

            private boolean isBold(TextPosition textPosition) {
                boolean isBold = false;
                RenderingMode renderingMode = getGraphicsState().getTextState().getRenderingMode();
                PDFont font = textPosition.getFont();
                if (font.getFontDescriptor() != null) {
                    isBold = font.getFontDescriptor().getFontWeight() > 700;
                }
                isBold = isBold || renderingMode == RenderingMode.FILL_STROKE || font.getName().contains("Bold");
                return isBold;
            }
        };

        stripper.setSortByPosition(true);
        stripper.setAddMoreFormatting(true);
        stripper.getText(document);

        return matches;
    }

    @Data
    public static class Match {
        private String text;
        private Rectangle boundingBox;
        private int pageNumber;
        private boolean isBold;
        private double wos;

        public Match(String text, Rectangle boundingBox, int pageNumber, boolean isBold, double wos) {
            this.text = text;
            this.boundingBox = boundingBox;
            this.pageNumber = pageNumber;
            this.isBold = isBold;
            this.wos = wos;
        }
    }
}
