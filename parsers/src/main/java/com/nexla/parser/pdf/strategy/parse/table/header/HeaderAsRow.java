package com.nexla.parser.pdf.strategy.parse.table.header;

import com.nexla.parser.pdf.strategy.config.TableConfig;
import com.nexla.parser.pdf.strategy.parse.Search;
import com.nexla.parser.pdf.strategy.parse.PDTable;
import com.nexla.parser.pdf.strategy.parse.geom.Rect;
import com.nexla.parser.pdf.strategy.parse.util.Lists;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.*;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;


public class HeaderAsRow implements Header {
    private static final Logger LOGGER = LoggerFactory.getLogger(HeaderAsRow.class);

    private final TableConfig tableConfig;
    private final PDDocument document;
    private final PDPage page;

    public HeaderAsRow(TableConfig tableConfig, PDDocument document, PDPage page) {
        this.tableConfig = tableConfig;
        this.document = document;
        this.page = page;
    }

    public List<PDTable.Tuple> find() {
        List<String> columns = TableConfig.collectNonBlank(tableConfig.getColumns());

        if (columns.isEmpty()) {
            LOGGER.warn("No non-blank columns for table " + tableConfig);
            return null;
        }

        int pageIndex = Lists.indexOf(document.getPages(), p -> p.equals(page));
        Search search = new Search(document, List.of(pageIndex));
        Collection<List<Search.Match>> matches = search
                .search(columns)
                .stream()
                .filter(m -> !tableConfig.isForceBoldHeaders() || m.isBold())
                .collect(Collectors.groupingBy(Search.Match::getText))
                .values();

        if (matches.size() != columns.size()) {
            LOGGER.warn("Could not find all columns for table " + tableConfig + " on page (0-based): " + pageIndex);
            return null;
        }

        return findTableHeaderRect(columns, matches);
    }

    private List<PDTable.Tuple> findTableHeaderRect(List<String> columns, Collection<List<Search.Match>> matches) {
        // here are the columns that are not TableConfig::isColumnBlank
        String current = columns.get(0);
        List<Search.Match> first = matches.stream().flatMap(List::stream).filter(m -> m.getText().equals(current)).collect(Collectors.toList());
        List<Search.Match> rest = matches.stream().flatMap(List::stream).filter(m -> !m.getText().equals(current)).collect(Collectors.toList());

        List<PDTable.Tuple> tuples = new ArrayList<>();
        for (Search.Match m : first) {
            List<PDTable.Cell> cells = findTableHeaderRect(m.getBoundingBox(), List.of(new PDTable.Cell(m.getText(), m.getBoundingBox())), columns.subList(1, columns.size()), rest);
            if (cells != null) {
                tuples.add(new PDTable.Tuple(m.getPageNumber(), cells));
            }
        }

        return tuples;
    }

    /**
     * Each next column should be to the right of the rectangle and on the same line (with some delta).
     *
     * @param header  possible header line
     * @param cells   already found columns
     * @param columns rest of the columns to find
     * @param rest    all matches for the rest of the columns
     * @return list of columns if all columns were found, null otherwise
     */
    private List<PDTable.Cell> findTableHeaderRect(Rectangle header, List<PDTable.Cell> cells, List<String> columns, List<Search.Match> rest) {
        if (columns.isEmpty() && rest.isEmpty()) {
            return cells;
        }

        if (columns.isEmpty() || rest.isEmpty()) {
            return null;
        }

        // start with the first column from the list
        String column = columns.get(0);
        // find all matches for the column which are to the right of the current rect (possible header line)
        List<Search.Match> next = rest
                .stream()
                .filter(m -> m.getText().equals(column))
                .filter(m -> header.getX() < m.getBoundingBox().getX())
                .sorted(Comparator.comparingDouble(x -> Rect.dx(x.getBoundingBox(), header)))
                .collect(Collectors.toList());

        for (Search.Match match : next) {
            // it is possible that the next column is not exactly the same centerY as the current rect (header may have several lines)
            if (Rect.intersectsY(header, match.getBoundingBox())) {
                List<PDTable.Cell> merged = new ArrayList<>(cells);
                merged.add(new PDTable.Cell(match.getText(), match.getBoundingBox()));

                List<String> restOfColumns = columns.subList(1, columns.size());
                List<Search.Match> restOfMatches = rest.stream().filter(m -> !m.getText().equals(column)).collect(Collectors.toList());

                return findTableHeaderRect(
                        Rect.merge(header, match.getBoundingBox()), merged, restOfColumns, restOfMatches
                );
            }
        }

        return null;
    }

}
