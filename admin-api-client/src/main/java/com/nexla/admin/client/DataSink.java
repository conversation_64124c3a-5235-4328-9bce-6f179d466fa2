package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.nexla.common.ConnectionType;
import com.nexla.connector.config.FlowType;
import com.nexla.connector.config.IngestionMode;
import com.nexla.telemetry.jmx.SimpleJmxMetrics;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@EqualsAndHashCode
@ToString
public class DataSink
    implements OwnerAndOrg, WithCreds, WithConnectionType, FlowNodeResource, WithClassVersion {
  public static final int CLASS_VERSION;

  static {
    CLASS_VERSION = WithClassVersion.calculateVersion(DataSink.class);
    SimpleJmxMetrics.gauge(() -> CLASS_VERSION, DataSink.class, "class", "version");
  }

  private Integer id;

  /*
   * "access_roles": [], "name": "S3 sink for Example Lat/Lng subscription",
   * "description": null, "status": null, "data_sub_id": 5006, "sink_type":
   * "s3", "sink_format": null, "sink_url": null, "sink_schedule": null,
   * "data_credentials": { "id": 5001, "owner": { "id": 2, "full_name":
   * "Jeff Williams" }, "org": { "id": 1, "name": "Nexla" }, "access_roles":
   * [], "credentials_type": "s3", "credentials_version": "1",
   * "credentials_enc":
   * "aVgIeY+BLiQjWS2eIZyEGaKH1P4qECelgbsr2dOPtSsDJIs768t/9ve9UTsIOTqUNL7v9mHvoKwHgiVv51MKHl/QUGsFfcwmPS59Fulr4AvxQff0J22Y0pd8D1D5TnUM9/mlVZupDs10r0W6Xc3gZq7F+3OrK0D1nk1pEPFa/gr7QlaLtmwtSw==",
   * "credentials_enc_iv": "tbv13SyPLKnEUny7", "updated_at":
   * "2016-10-31T14:28:39.000Z", "created_at": "2016-10-31T14:28:39.000Z"
   */
  @JsonProperty("sink_type")
  private ConnectionType connectionType;

  private ResourceStatus status;

  @JsonProperty("sink_format")
  private String sinkFormat;

  @JsonProperty("data_credentials")
  private DataCredentials dataCredentials;

  @JsonProperty("sink_url")
  private String sinkUrl;

  @JsonProperty("sink_schedule")
  private String sinkSchedule;

  @JsonProperty("sink_config")
  private Map<String, Object> sinkConfig;

  @JsonProperty("owner")
  private Owner owner;

  @JsonProperty("org")
  private Org org;

  private Integer version;

  private String name;

  @JsonProperty("data_map")
  private DataMap dataMap;

  @JsonProperty("data_set_id")
  private Integer dataSetId;

  @JsonProperty("data_set")
  private DataSet dataSet;

  @JsonProperty("script_config")
  private Optional<ScriptConfig> scriptConfig = Optional.empty();

  @Override
  public Owner getOwner() {
    return owner;
  }

  @Override
  public Org getOrg() {
    return org;
  }

  // aka the main flow id
  @JsonProperty("origin_node_id")
  public Integer originNodeId;

  @JsonProperty("flow_type")
  public FlowType flowType;

  @JsonProperty("ingestion_mode")
  private IngestionMode ingestionMode;

  @JsonProperty("flow_node_id")
  public Integer flowNodeId;

  @JsonProperty("referenced_resource_ids")
  private Optional<ReferencedResourceIds> referencedResourceIds = Optional.empty();

  @JsonProperty(OBJECT_VERSION_JSON_PROPERTY)
  private Integer objectVersion = -1;

  @JsonProperty("flow_triggers")
  private List<FlowTrigger> flowTriggers = new ArrayList<>();

  // added because we need to track original flow type after parsed to enum,
  // once we add this capability to FlowType class we can remove this property
  @JsonIgnore public String originalFlowType;

  @JsonSetter("flow_type")
  public void setFlowTypes(String flowType) {
    this.flowType = FlowType.fromString(flowType);
    this.originalFlowType = flowType;
  }

  @Override
  public DataSink setObjectVersion(Integer objectVersion) {
    this.objectVersion = objectVersion;
    return this;
  }

  @Override
  @JsonIgnore
  public int getClassVersion() {
    return CLASS_VERSION;
  }
}
