/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.postgres.tables;

import com.nexla.db.postgres.DefaultSchema;
import com.nexla.db.postgres.Keys;
import com.nexla.db.postgres.tables.records.FastOffsetRecord;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row4;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class FastOffset extends TableImpl<FastOffsetRecord> {

  private static final long serialVersionUID = 1L;

  /** The reference instance of <code>fast_offset</code> */
  public static final FastOffset FAST_OFFSET = new FastOffset();

  /** The class holding records for this type */
  @Override
  public Class<FastOffsetRecord> getRecordType() {
    return FastOffsetRecord.class;
  }

  /** The column <code>fast_offset.source_id</code>. */
  public final TableField<FastOffsetRecord, Integer> SOURCE_ID =
      createField(DSL.name("source_id"), SQLDataType.INTEGER.nullable(false), this, "");

  /** The column <code>fast_offset.sink_id</code>. */
  public final TableField<FastOffsetRecord, Integer> SINK_ID =
      createField(DSL.name("sink_id"), SQLDataType.INTEGER.nullable(false), this, "");

  /** The column <code>fast_offset.offset_position</code>. */
  public final TableField<FastOffsetRecord, String> OFFSET_POSITION =
      createField(DSL.name("offset_position"), SQLDataType.CLOB, this, "");

  /** The column <code>fast_offset.last_modified</code>. */
  public final TableField<FastOffsetRecord, LocalDateTime> LAST_MODIFIED =
      createField(
          DSL.name("last_modified"), SQLDataType.LOCALDATETIME(3).nullable(false), this, "");

  private FastOffset(Name alias, Table<FastOffsetRecord> aliased) {
    this(alias, aliased, null);
  }

  private FastOffset(Name alias, Table<FastOffsetRecord> aliased, Field<?>[] parameters) {
    super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
  }

  /** Create an aliased <code>fast_offset</code> table reference */
  public FastOffset(String alias) {
    this(DSL.name(alias), FAST_OFFSET);
  }

  /** Create an aliased <code>fast_offset</code> table reference */
  public FastOffset(Name alias) {
    this(alias, FAST_OFFSET);
  }

  /** Create a <code>fast_offset</code> table reference */
  public FastOffset() {
    this(DSL.name("fast_offset"), null);
  }

  public <O extends Record> FastOffset(Table<O> child, ForeignKey<O, FastOffsetRecord> key) {
    super(child, key, FAST_OFFSET);
  }

  @Override
  public Schema getSchema() {
    return DefaultSchema.DEFAULT_SCHEMA;
  }

  @Override
  public UniqueKey<FastOffsetRecord> getPrimaryKey() {
    return Keys.FAST_OFFSET_PKEY;
  }

  @Override
  public List<UniqueKey<FastOffsetRecord>> getKeys() {
    return Arrays.<UniqueKey<FastOffsetRecord>>asList(Keys.FAST_OFFSET_PKEY);
  }

  @Override
  public FastOffset as(String alias) {
    return new FastOffset(DSL.name(alias), this);
  }

  @Override
  public FastOffset as(Name alias) {
    return new FastOffset(alias, this);
  }

  /** Rename this table */
  @Override
  public FastOffset rename(String name) {
    return new FastOffset(DSL.name(name), null);
  }

  /** Rename this table */
  @Override
  public FastOffset rename(Name name) {
    return new FastOffset(name, null);
  }

  // -------------------------------------------------------------------------
  // Row4 type methods
  // -------------------------------------------------------------------------

  @Override
  public Row4<Integer, Integer, String, LocalDateTime> fieldsRow() {
    return (Row4) super.fieldsRow();
  }
}
