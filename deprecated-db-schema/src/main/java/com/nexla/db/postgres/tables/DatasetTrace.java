/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.postgres.tables;

import com.nexla.db.postgres.DefaultSchema;
import com.nexla.db.postgres.Keys;
import com.nexla.db.postgres.tables.records.DatasetTraceRecord;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row7;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class DatasetTrace extends TableImpl<DatasetTraceRecord> {

  private static final long serialVersionUID = 1L;

  /** The reference instance of <code>dataset_trace</code> */
  public static final DatasetTrace DATASET_TRACE = new DatasetTrace();

  /** The class holding records for this type */
  @Override
  public Class<DatasetTraceRecord> getRecordType() {
    return DatasetTraceRecord.class;
  }

  /** The column <code>dataset_trace.resource_id</code>. */
  public final TableField<DatasetTraceRecord, Integer> RESOURCE_ID =
      createField(DSL.name("resource_id"), SQLDataType.INTEGER.nullable(false), this, "");

  /** The column <code>dataset_trace.run_id</code>. */
  public final TableField<DatasetTraceRecord, Long> RUN_ID =
      createField(DSL.name("run_id"), SQLDataType.BIGINT.nullable(false), this, "");

  /** The column <code>dataset_trace.traces</code>. */
  public final TableField<DatasetTraceRecord, Integer> TRACES =
      createField(DSL.name("traces"), SQLDataType.INTEGER.nullable(false), this, "");

  /** The column <code>dataset_trace.tx_state</code>. */
  public final TableField<DatasetTraceRecord, String> TX_STATE =
      createField(DSL.name("tx_state"), SQLDataType.VARCHAR(36).nullable(false), this, "");

  /** The column <code>dataset_trace.tx_done_ts</code>. */
  public final TableField<DatasetTraceRecord, LocalDateTime> TX_DONE_TS =
      createField(DSL.name("tx_done_ts"), SQLDataType.LOCALDATETIME(3), this, "");

  /** The column <code>dataset_trace.updated_at</code>. */
  public final TableField<DatasetTraceRecord, LocalDateTime> UPDATED_AT =
      createField(DSL.name("updated_at"), SQLDataType.LOCALDATETIME(3), this, "");

  /** The column <code>dataset_trace.created_at</code>. */
  public final TableField<DatasetTraceRecord, LocalDateTime> CREATED_AT =
      createField(DSL.name("created_at"), SQLDataType.LOCALDATETIME(3), this, "");

  private DatasetTrace(Name alias, Table<DatasetTraceRecord> aliased) {
    this(alias, aliased, null);
  }

  private DatasetTrace(Name alias, Table<DatasetTraceRecord> aliased, Field<?>[] parameters) {
    super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
  }

  /** Create an aliased <code>dataset_trace</code> table reference */
  public DatasetTrace(String alias) {
    this(DSL.name(alias), DATASET_TRACE);
  }

  /** Create an aliased <code>dataset_trace</code> table reference */
  public DatasetTrace(Name alias) {
    this(alias, DATASET_TRACE);
  }

  /** Create a <code>dataset_trace</code> table reference */
  public DatasetTrace() {
    this(DSL.name("dataset_trace"), null);
  }

  public <O extends Record> DatasetTrace(Table<O> child, ForeignKey<O, DatasetTraceRecord> key) {
    super(child, key, DATASET_TRACE);
  }

  @Override
  public Schema getSchema() {
    return DefaultSchema.DEFAULT_SCHEMA;
  }

  @Override
  public UniqueKey<DatasetTraceRecord> getPrimaryKey() {
    return Keys.KEY_DATASET_TRACE_PRIMARY;
  }

  @Override
  public List<UniqueKey<DatasetTraceRecord>> getKeys() {
    return Arrays.<UniqueKey<DatasetTraceRecord>>asList(Keys.KEY_DATASET_TRACE_PRIMARY);
  }

  @Override
  public DatasetTrace as(String alias) {
    return new DatasetTrace(DSL.name(alias), this);
  }

  @Override
  public DatasetTrace as(Name alias) {
    return new DatasetTrace(alias, this);
  }

  /** Rename this table */
  @Override
  public DatasetTrace rename(String name) {
    return new DatasetTrace(DSL.name(name), null);
  }

  /** Rename this table */
  @Override
  public DatasetTrace rename(Name name) {
    return new DatasetTrace(name, null);
  }

  // -------------------------------------------------------------------------
  // Row7 type methods
  // -------------------------------------------------------------------------

  @Override
  public Row7<Integer, Long, Integer, String, LocalDateTime, LocalDateTime, LocalDateTime>
      fieldsRow() {
    return (Row7) super.fieldsRow();
  }
}
