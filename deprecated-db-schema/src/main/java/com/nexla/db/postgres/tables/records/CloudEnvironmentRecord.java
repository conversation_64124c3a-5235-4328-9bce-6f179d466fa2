package com.nexla.db.postgres.tables.records;

import com.nexla.db.postgres.tables.CloudEnvironment;
import java.time.LocalDateTime;
import org.jooq.Field;
import org.jooq.Record4;
import org.jooq.Record5;
import org.jooq.Row5;
import org.jooq.impl.UpdatableRecordImpl;

@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class CloudEnvironmentRecord extends UpdatableRecordImpl<CloudEnvironmentRecord>
    implements Record5<Integer, Integer, String, LocalDateTime, String> {

  private static final long serialVersionUID = 1L;

  /** Setter for <code>source_id</code>. */
  public void setSourceId(Integer value) {
    set(0, value);
  }

  /** Setter for <code>sink_id</code>. */
  public void setSinkId(Integer value) {
    set(1, value);
  }

  /** Setter for <code>cluster_id</code>. */
  public void setClusterId(String value) {
    set(2, value);
  }

  /** Setter for <code>last_operation_ts</code>. */
  public void setLastOperationTs(LocalDateTime value) {
    set(3, value);
  }

  /** Setter for <code>last_status</code>. */
  public void setLastStatus(String value) {
    set(4, value);
  }

  /** Getter for <code>source_id</code>. */
  public Integer getSourceId() {
    return (Integer) get(0);
  }

  /** Getter for <code>sink_id</code>. */
  public Integer getSinkId() {
    return (Integer) get(1);
  }

  /** Getter for <code>cluster_id</code>. */
  public String getClusterId() {
    return (String) get(2);
  }

  /** Getter for <code>last_operation_ts</code>. */
  public LocalDateTime getLastOperationTs() {
    return (LocalDateTime) get(3);
  }

  /** Getter for <code>last_status</code>. */
  public String getLastStatus() {
    return (String) get(4);
  }

  // -------------------------------------------------------------------------
  // Primary key information
  // -------------------------------------------------------------------------

  @Override
  public Record4<Integer, Integer, String, String> key() {
    return (Record4) super.key();
  }

  // -------------------------------------------------------------------------
  // Record6 type implementation
  // -------------------------------------------------------------------------

  @Override
  public Row5<Integer, Integer, String, LocalDateTime, String> fieldsRow() {
    return (Row5) super.fieldsRow();
  }

  @Override
  public Row5<Integer, Integer, String, LocalDateTime, String> valuesRow() {
    return (Row5) super.valuesRow();
  }

  @Override
  public Field<Integer> field1() {
    return CloudEnvironment.CLOUD_ENVIRONMENT.SOURCE_ID;
  }

  @Override
  public Field<Integer> field2() {
    return CloudEnvironment.CLOUD_ENVIRONMENT.SINK_ID;
  }

  @Override
  public Field<String> field3() {
    return CloudEnvironment.CLOUD_ENVIRONMENT.CLUSTER_ID;
  }

  @Override
  public Field<LocalDateTime> field4() {
    return CloudEnvironment.CLOUD_ENVIRONMENT.LAST_OPERATION_TS;
  }

  @Override
  public Field<String> field5() {
    return CloudEnvironment.CLOUD_ENVIRONMENT.LAST_STATUS;
  }

  @Override
  public Integer component1() {
    return getSourceId();
  }

  @Override
  public Integer component2() {
    return getSinkId();
  }

  @Override
  public String component3() {
    return getClusterId();
  }

  @Override
  public LocalDateTime component4() {
    return getLastOperationTs();
  }

  @Override
  public String component5() {
    return getLastStatus();
  }

  @Override
  public Integer value1() {
    return getSourceId();
  }

  @Override
  public Integer value2() {
    return getSinkId();
  }

  @Override
  public String value3() {
    return getClusterId();
  }

  @Override
  public LocalDateTime value4() {
    return getLastOperationTs();
  }

  @Override
  public String value5() {
    return getLastStatus();
  }

  @Override
  public CloudEnvironmentRecord value1(Integer value) {
    setSourceId(value);
    return this;
  }

  @Override
  public CloudEnvironmentRecord value2(Integer value) {
    setSinkId(value);
    return this;
  }

  @Override
  public CloudEnvironmentRecord value3(String value) {
    setClusterId(value);
    return this;
  }

  @Override
  public CloudEnvironmentRecord value4(LocalDateTime value) {
    setLastOperationTs(value);
    return this;
  }

  @Override
  public CloudEnvironmentRecord value5(String value) {
    setLastStatus(value);
    return this;
  }

  @Override
  public CloudEnvironmentRecord values(
      Integer value1, Integer value2, String value3, LocalDateTime value4, String value5) {
    value1(value1);
    value2(value2);
    value3(value3);
    value4(value4);
    value5(value5);
    return this;
  }

  // -------------------------------------------------------------------------
  // Constructors
  // -------------------------------------------------------------------------

  /** Create a detached SemaphoreRecord */
  public CloudEnvironmentRecord() {
    super(CloudEnvironment.CLOUD_ENVIRONMENT);
  }

  /** Create a detached, initialised CloudEnvironmentRecord */
  public CloudEnvironmentRecord(
      Integer sourceId,
      Integer sinkId,
      String clusterId,
      LocalDateTime lastOperationTs,
      String lastStatus) {
    super(CloudEnvironment.CLOUD_ENVIRONMENT);

    setSourceId(sourceId);
    setSinkId(sinkId);
    setClusterId(clusterId);
    setLastOperationTs(lastOperationTs);
    setLastStatus(lastStatus);
  }
}
