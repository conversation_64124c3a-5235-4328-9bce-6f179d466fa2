/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.postgres.tables;

import com.nexla.db.postgres.DefaultSchema;
import com.nexla.db.postgres.Keys;
import com.nexla.db.postgres.tables.records.AuditKeyValueRecord;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row5;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class AuditKeyValue extends TableImpl<AuditKeyValueRecord> {

  private static final long serialVersionUID = 1L;

  /** The reference instance of <code>audit_key_value</code> */
  public static final AuditKeyValue AUDIT_KEY_VALUE = new AuditKeyValue();

  /** The class holding records for this type */
  @Override
  public Class<AuditKeyValueRecord> getRecordType() {
    return AuditKeyValueRecord.class;
  }

  /** The column <code>audit_key_value.id</code>. */
  public final TableField<AuditKeyValueRecord, Long> ID =
      createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "");

  /** The column <code>audit_key_value.action</code>. */
  public final TableField<AuditKeyValueRecord, String> ACTION =
      createField(DSL.name("action"), SQLDataType.VARCHAR(10).nullable(false), this, "");

  /** The column <code>audit_key_value.audit_ts</code>. */
  public final TableField<AuditKeyValueRecord, LocalDateTime> AUDIT_TS =
      createField(DSL.name("audit_ts"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

  /** The column <code>audit_key_value.vendor_key</code>. */
  public final TableField<AuditKeyValueRecord, String> VENDOR_KEY =
      createField(DSL.name("vendor_key"), SQLDataType.VARCHAR(100).nullable(false), this, "");

  /** The column <code>audit_key_value.vendor_value</code>. */
  public final TableField<AuditKeyValueRecord, String> VENDOR_VALUE =
      createField(DSL.name("vendor_value"), SQLDataType.CLOB.nullable(false), this, "");

  private AuditKeyValue(Name alias, Table<AuditKeyValueRecord> aliased) {
    this(alias, aliased, null);
  }

  private AuditKeyValue(Name alias, Table<AuditKeyValueRecord> aliased, Field<?>[] parameters) {
    super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
  }

  /** Create an aliased <code>audit_key_value</code> table reference */
  public AuditKeyValue(String alias) {
    this(DSL.name(alias), AUDIT_KEY_VALUE);
  }

  /** Create an aliased <code>audit_key_value</code> table reference */
  public AuditKeyValue(Name alias) {
    this(alias, AUDIT_KEY_VALUE);
  }

  /** Create a <code>audit_key_value</code> table reference */
  public AuditKeyValue() {
    this(DSL.name("audit_key_value"), null);
  }

  public <O extends Record> AuditKeyValue(Table<O> child, ForeignKey<O, AuditKeyValueRecord> key) {
    super(child, key, AUDIT_KEY_VALUE);
  }

  @Override
  public Schema getSchema() {
    return DefaultSchema.DEFAULT_SCHEMA;
  }

  @Override
  public List<Index> getIndexes() {
    return Collections.emptyList();
  }

  @Override
  public Identity<AuditKeyValueRecord, Long> getIdentity() {
    return (Identity<AuditKeyValueRecord, Long>) super.getIdentity();
  }

  @Override
  public UniqueKey<AuditKeyValueRecord> getPrimaryKey() {
    return Keys.AUDIT_KEY_VALUE_PKEY;
  }

  @Override
  public List<UniqueKey<AuditKeyValueRecord>> getKeys() {
    return Arrays.<UniqueKey<AuditKeyValueRecord>>asList(Keys.AUDIT_KEY_VALUE_PKEY);
  }

  @Override
  public AuditKeyValue as(String alias) {
    return new AuditKeyValue(DSL.name(alias), this);
  }

  @Override
  public AuditKeyValue as(Name alias) {
    return new AuditKeyValue(alias, this);
  }

  /** Rename this table */
  @Override
  public AuditKeyValue rename(String name) {
    return new AuditKeyValue(DSL.name(name), null);
  }

  /** Rename this table */
  @Override
  public AuditKeyValue rename(Name name) {
    return new AuditKeyValue(name, null);
  }

  // -------------------------------------------------------------------------
  // Row5 type methods
  // -------------------------------------------------------------------------

  @Override
  public Row5<Long, String, LocalDateTime, String, String> fieldsRow() {
    return (Row5) super.fieldsRow();
  }
}
