/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.postgres;

import com.nexla.db.postgres.tables.FileListing;
import com.nexla.db.postgres.tables.PipelineRunState;
import org.jooq.Index;
import org.jooq.OrderField;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;

/** A class modelling indexes of tables in the default schema. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class Indexes {

  // -------------------------------------------------------------------------
  // INDEX definitions
  // -------------------------------------------------------------------------

  public static final Index FILE_LISTING_FILE_LISTING_FULL_PATH_RESOUCE_ID =
      Internal.createIndex(
          DSL.name("file_listing_full_path_resouce_id"),
          FileListing.FILE_LISTING,
          new OrderField[] {
            FileListing.FILE_LISTING.RESOURCE_ID, FileListing.FILE_LISTING.FULL_PATH
          },
          false);
  public static final Index FILE_LISTING_FILE_LISTING_RESOURCE_ID_STATUS =
      Internal.createIndex(
          DSL.name("file_listing_resource_id_status"),
          FileListing.FILE_LISTING,
          new OrderField[] {FileListing.FILE_LISTING.RESOURCE_ID, FileListing.FILE_LISTING.STATUS},
          false);
  public static final Index FILE_LISTING_FILE_LISTING_STATUS =
      Internal.createIndex(
          DSL.name("file_listing_status"),
          FileListing.FILE_LISTING,
          new OrderField[] {FileListing.FILE_LISTING.STATUS},
          false);
  public static final Index PIPELINE_RUN_STATE_PIPELINE_RUN_STATE_LAST_MODIFIED_IDX =
      Internal.createIndex(
          DSL.name("pipeline_run_state_last_modified_IDX"),
          PipelineRunState.PIPELINE_RUN_STATE,
          new OrderField[] {PipelineRunState.PIPELINE_RUN_STATE.LAST_MODIFIED},
          false);
  public static final Index PIPELINE_RUN_STATE_PIPELINE_RUN_STATE_STATUS_IDX =
      Internal.createIndex(
          DSL.name("pipeline_run_state_status_IDX"),
          PipelineRunState.PIPELINE_RUN_STATE,
          new OrderField[] {PipelineRunState.PIPELINE_RUN_STATE.STATUS},
          false);
}
