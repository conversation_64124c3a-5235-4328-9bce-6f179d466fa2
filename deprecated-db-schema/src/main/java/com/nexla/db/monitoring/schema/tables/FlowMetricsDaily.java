/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.monitoring.schema.tables;

import com.nexla.db.monitoring.schema.DefaultSchema;
import com.nexla.db.monitoring.schema.Keys;
import com.nexla.db.monitoring.schema.tables.records.FlowMetricsDailyRecord;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row11;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class FlowMetricsDaily extends TableImpl<FlowMetricsDailyRecord> {

  private static final long serialVersionUID = -945154495;

  /** The reference instance of <code>flow_metrics_daily</code> */
  public static final FlowMetricsDaily FLOW_METRICS_DAILY = new FlowMetricsDaily();

  /** The class holding records for this type */
  @Override
  public Class<FlowMetricsDailyRecord> getRecordType() {
    return FlowMetricsDailyRecord.class;
  }

  /** The column <code>flow_metrics_daily.id</code>. */
  public final TableField<FlowMetricsDailyRecord, Long> ID =
      createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "");

  /** The column <code>flow_metrics_daily.origin_node_id</code>. */
  public final TableField<FlowMetricsDailyRecord, Integer> ORIGIN_NODE_ID =
      createField(DSL.name("origin_node_id"), SQLDataType.INTEGER.nullable(false), this, "");

  /** The column <code>flow_metrics_daily.org_id</code>. */
  public final TableField<FlowMetricsDailyRecord, Integer> ORG_ID =
      createField(DSL.name("org_id"), SQLDataType.INTEGER.nullable(false), this, "");

  /** The column <code>flow_metrics_daily.owner_id</code>. */
  public final TableField<FlowMetricsDailyRecord, Integer> OWNER_ID =
      createField(DSL.name("owner_id"), SQLDataType.INTEGER.nullable(false), this, "");

  /** The column <code>flow_metrics_daily.run_id</code>. */
  public final TableField<FlowMetricsDailyRecord, Long> RUN_ID =
      createField(DSL.name("run_id"), SQLDataType.BIGINT.nullable(false), this, "");

  /** The column <code>flow_metrics_daily.row_count</code>. */
  public final TableField<FlowMetricsDailyRecord, Long> ROW_COUNT =
      createField(DSL.name("row_count"), SQLDataType.BIGINT.nullable(false), this, "");

  /** The column <code>flow_metrics_daily.error_count</code>. */
  public final TableField<FlowMetricsDailyRecord, Long> ERROR_COUNT =
      createField(DSL.name("error_count"), SQLDataType.BIGINT.nullable(false), this, "");

  /** The column <code>flow_metrics_daily.size_value</code>. */
  public final TableField<FlowMetricsDailyRecord, Long> SIZE_VALUE =
      createField(DSL.name("size_value"), SQLDataType.BIGINT.nullable(false), this, "");

  /** The column <code>flow_metrics_daily.reporting_date</code>. */
  public final TableField<FlowMetricsDailyRecord, LocalDateTime> REPORTING_DATE =
      createField(
          DSL.name("reporting_date"),
          SQLDataType.LOCALDATETIME(3)
              .nullable(false)
              .defaultValue(DSL.field("CURRENT_TIMESTAMP(3)", SQLDataType.LOCALDATETIME)),
          this,
          "");

  /** The column <code>flow_metrics_daily.created_at</code>. */
  public final TableField<FlowMetricsDailyRecord, LocalDateTime> CREATED_AT =
      createField(
          DSL.name("created_at"),
          SQLDataType.LOCALDATETIME(3)
              .nullable(false)
              .defaultValue(DSL.field("CURRENT_TIMESTAMP(3)", SQLDataType.LOCALDATETIME)),
          this,
          "");

  /** The column <code>flow_metrics_daily.updated_at</code>. */
  public final TableField<FlowMetricsDailyRecord, LocalDateTime> UPDATED_AT =
      createField(
          DSL.name("updated_at"),
          SQLDataType.LOCALDATETIME(3)
              .nullable(false)
              .defaultValue(DSL.field("CURRENT_TIMESTAMP(3)", SQLDataType.LOCALDATETIME)),
          this,
          "");

  private FlowMetricsDaily(Name alias, Table<FlowMetricsDailyRecord> aliased) {
    this(alias, aliased, null);
  }

  private FlowMetricsDaily(
      Name alias, Table<FlowMetricsDailyRecord> aliased, Field<?>[] parameters) {
    super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
  }

  /** Create an aliased <code>flow_metrics_daily</code> table reference */
  public FlowMetricsDaily(String alias) {
    this(DSL.name(alias), FLOW_METRICS_DAILY);
  }

  /** Create an aliased <code>flow_metrics_daily</code> table reference */
  public FlowMetricsDaily(Name alias) {
    this(alias, FLOW_METRICS_DAILY);
  }

  /** Create a <code>flow_metrics_daily</code> table reference */
  public FlowMetricsDaily() {
    this(DSL.name("flow_metrics_daily"), null);
  }

  public <O extends Record> FlowMetricsDaily(
      Table<O> child, ForeignKey<O, FlowMetricsDailyRecord> key) {
    super(child, key, FLOW_METRICS_DAILY);
  }

  @Override
  public Schema getSchema() {
    return DefaultSchema.DEFAULT_SCHEMA;
  }

  @Override
  public Identity<FlowMetricsDailyRecord, Long> getIdentity() {
    return (Identity<FlowMetricsDailyRecord, Long>) super.getIdentity();
  }

  @Override
  public UniqueKey<FlowMetricsDailyRecord> getPrimaryKey() {
    return Keys.KEY_FLOW_METRICS_DAILY_PRIMARY;
  }

  @Override
  public List<UniqueKey<FlowMetricsDailyRecord>> getKeys() {
    return Arrays.<UniqueKey<FlowMetricsDailyRecord>>asList(Keys.KEY_FLOW_METRICS_DAILY_PRIMARY);
  }

  @Override
  public FlowMetricsDaily as(String alias) {
    return new FlowMetricsDaily(DSL.name(alias), this);
  }

  @Override
  public FlowMetricsDaily as(Name alias) {
    return new FlowMetricsDaily(alias, this);
  }

  /** Rename this table */
  @Override
  public FlowMetricsDaily rename(String name) {
    return new FlowMetricsDaily(DSL.name(name), null);
  }

  /** Rename this table */
  @Override
  public FlowMetricsDaily rename(Name name) {
    return new FlowMetricsDaily(name, null);
  }

  // -------------------------------------------------------------------------
  // Row11 type methods
  // -------------------------------------------------------------------------

  @Override
  public Row11<
          Long,
          Integer,
          Integer,
          Integer,
          Long,
          Long,
          Long,
          Long,
          LocalDateTime,
          LocalDateTime,
          LocalDateTime>
      fieldsRow() {
    return (Row11) super.fieldsRow();
  }
}
