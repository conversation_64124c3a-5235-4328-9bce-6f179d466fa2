/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.monitoring.schema.tables.records;

import com.nexla.db.monitoring.schema.tables.QuarantineFiles;
import java.time.LocalDateTime;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record16;
import org.jooq.Row16;
import org.jooq.impl.UpdatableRecordImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class QuarantineFilesRecord extends UpdatableRecordImpl<QuarantineFilesRecord>
    implements Record16<
        Long,
        String,
        Integer,
        Integer,
        String,
        String,
        Long,
        Long,
        Integer,
        String,
        Integer,
        Integer,
        Integer,
        LocalDateTime,
        LocalDateTime,
        Byte> {

  private static final long serialVersionUID = 1L;

  /** Setter for <code>quarantine_files.id</code>. */
  public void setId(Long value) {
    set(0, value);
  }

  /** Getter for <code>quarantine_files.id</code>. */
  public Long getId() {
    return (Long) get(0);
  }

  /** Setter for <code>quarantine_files.resource_type</code>. */
  public void setResourceType(String value) {
    set(1, value);
  }

  /** Getter for <code>quarantine_files.resource_type</code>. */
  public String getResourceType() {
    return (String) get(1);
  }

  /** Setter for <code>quarantine_files.resource_id</code>. */
  public void setResourceId(Integer value) {
    set(2, value);
  }

  /** Getter for <code>quarantine_files.resource_id</code>. */
  public Integer getResourceId() {
    return (Integer) get(2);
  }

  /** Setter for <code>quarantine_files.dataset_id</code>. */
  public void setDatasetId(Integer value) {
    set(3, value);
  }

  /** Getter for <code>quarantine_files.dataset_id</code>. */
  public Integer getDatasetId() {
    return (Integer) get(3);
  }

  /** Setter for <code>quarantine_files.name</code>. */
  public void setName(String value) {
    set(4, value);
  }

  /** Getter for <code>quarantine_files.name</code>. */
  public String getName() {
    return (String) get(4);
  }

  /** Setter for <code>quarantine_files.bucket</code>. */
  public void setBucket(String value) {
    set(5, value);
  }

  /** Getter for <code>quarantine_files.bucket</code>. */
  public String getBucket() {
    return (String) get(5);
  }

  /** Setter for <code>quarantine_files.size_value</code>. */
  public void setSizeValue(Long value) {
    set(6, value);
  }

  /** Getter for <code>quarantine_files.size_value</code>. */
  public Long getSizeValue() {
    return (Long) get(6);
  }

  /** Setter for <code>quarantine_files.record_count</code>. */
  public void setRecordCount(Long value) {
    set(7, value);
  }

  /** Getter for <code>quarantine_files.record_count</code>. */
  public Long getRecordCount() {
    return (Long) get(7);
  }

  /** Setter for <code>quarantine_files.data_credentials_id</code>. */
  public void setDataCredentialsId(Integer value) {
    set(8, value);
  }

  /** Getter for <code>quarantine_files.data_credentials_id</code>. */
  public Integer getDataCredentialsId() {
    return (Integer) get(8);
  }

  /** Setter for <code>quarantine_files.cron_frequency</code>. */
  public void setCronFrequency(String value) {
    set(9, value);
  }

  /** Getter for <code>quarantine_files.cron_frequency</code>. */
  public String getCronFrequency() {
    return (String) get(9);
  }

  /** Setter for <code>quarantine_files.quarantine_setting_id</code>. */
  public void setQuarantineSettingId(Integer value) {
    set(10, value);
  }

  /** Getter for <code>quarantine_files.quarantine_setting_id</code>. */
  public Integer getQuarantineSettingId() {
    return (Integer) get(10);
  }

  /** Setter for <code>quarantine_files.org_id</code>. */
  public void setOrgId(Integer value) {
    set(11, value);
  }

  /** Getter for <code>quarantine_files.org_id</code>. */
  public Integer getOrgId() {
    return (Integer) get(11);
  }

  /** Setter for <code>quarantine_files.owner_id</code>. */
  public void setOwnerId(Integer value) {
    set(12, value);
  }

  /** Getter for <code>quarantine_files.owner_id</code>. */
  public Integer getOwnerId() {
    return (Integer) get(12);
  }

  /** Setter for <code>quarantine_files.updated_at</code>. */
  public void setUpdatedAt(LocalDateTime value) {
    set(13, value);
  }

  /** Getter for <code>quarantine_files.updated_at</code>. */
  public LocalDateTime getUpdatedAt() {
    return (LocalDateTime) get(13);
  }

  /** Setter for <code>quarantine_files.created_at</code>. */
  public void setCreatedAt(LocalDateTime value) {
    set(14, value);
  }

  /** Getter for <code>quarantine_files.created_at</code>. */
  public LocalDateTime getCreatedAt() {
    return (LocalDateTime) get(14);
  }

  /** Setter for <code>quarantine_files.system_quarantine</code>. */
  public void setSystemQuarantine(Byte value) {
    set(15, value);
  }

  /** Getter for <code>quarantine_files.system_quarantine</code>. */
  public Byte getSystemQuarantine() {
    return (Byte) get(15);
  }

  // -------------------------------------------------------------------------
  // Primary key information
  // -------------------------------------------------------------------------

  @Override
  public Record1<Long> key() {
    return (Record1) super.key();
  }

  // -------------------------------------------------------------------------
  // Record16 type implementation
  // -------------------------------------------------------------------------

  @Override
  public Row16<
          Long,
          String,
          Integer,
          Integer,
          String,
          String,
          Long,
          Long,
          Integer,
          String,
          Integer,
          Integer,
          Integer,
          LocalDateTime,
          LocalDateTime,
          Byte>
      fieldsRow() {
    return (Row16) super.fieldsRow();
  }

  @Override
  public Row16<
          Long,
          String,
          Integer,
          Integer,
          String,
          String,
          Long,
          Long,
          Integer,
          String,
          Integer,
          Integer,
          Integer,
          LocalDateTime,
          LocalDateTime,
          Byte>
      valuesRow() {
    return (Row16) super.valuesRow();
  }

  @Override
  public Field<Long> field1() {
    return QuarantineFiles.QUARANTINE_FILES.ID;
  }

  @Override
  public Field<String> field2() {
    return QuarantineFiles.QUARANTINE_FILES.RESOURCE_TYPE;
  }

  @Override
  public Field<Integer> field3() {
    return QuarantineFiles.QUARANTINE_FILES.RESOURCE_ID;
  }

  @Override
  public Field<Integer> field4() {
    return QuarantineFiles.QUARANTINE_FILES.DATASET_ID;
  }

  @Override
  public Field<String> field5() {
    return QuarantineFiles.QUARANTINE_FILES.NAME;
  }

  @Override
  public Field<String> field6() {
    return QuarantineFiles.QUARANTINE_FILES.BUCKET;
  }

  @Override
  public Field<Long> field7() {
    return QuarantineFiles.QUARANTINE_FILES.SIZE_VALUE;
  }

  @Override
  public Field<Long> field8() {
    return QuarantineFiles.QUARANTINE_FILES.RECORD_COUNT;
  }

  @Override
  public Field<Integer> field9() {
    return QuarantineFiles.QUARANTINE_FILES.DATA_CREDENTIALS_ID;
  }

  @Override
  public Field<String> field10() {
    return QuarantineFiles.QUARANTINE_FILES.CRON_FREQUENCY;
  }

  @Override
  public Field<Integer> field11() {
    return QuarantineFiles.QUARANTINE_FILES.QUARANTINE_SETTING_ID;
  }

  @Override
  public Field<Integer> field12() {
    return QuarantineFiles.QUARANTINE_FILES.ORG_ID;
  }

  @Override
  public Field<Integer> field13() {
    return QuarantineFiles.QUARANTINE_FILES.OWNER_ID;
  }

  @Override
  public Field<LocalDateTime> field14() {
    return QuarantineFiles.QUARANTINE_FILES.UPDATED_AT;
  }

  @Override
  public Field<LocalDateTime> field15() {
    return QuarantineFiles.QUARANTINE_FILES.CREATED_AT;
  }

  @Override
  public Field<Byte> field16() {
    return QuarantineFiles.QUARANTINE_FILES.SYSTEM_QUARANTINE;
  }

  @Override
  public Long component1() {
    return getId();
  }

  @Override
  public String component2() {
    return getResourceType();
  }

  @Override
  public Integer component3() {
    return getResourceId();
  }

  @Override
  public Integer component4() {
    return getDatasetId();
  }

  @Override
  public String component5() {
    return getName();
  }

  @Override
  public String component6() {
    return getBucket();
  }

  @Override
  public Long component7() {
    return getSizeValue();
  }

  @Override
  public Long component8() {
    return getRecordCount();
  }

  @Override
  public Integer component9() {
    return getDataCredentialsId();
  }

  @Override
  public String component10() {
    return getCronFrequency();
  }

  @Override
  public Integer component11() {
    return getQuarantineSettingId();
  }

  @Override
  public Integer component12() {
    return getOrgId();
  }

  @Override
  public Integer component13() {
    return getOwnerId();
  }

  @Override
  public LocalDateTime component14() {
    return getUpdatedAt();
  }

  @Override
  public LocalDateTime component15() {
    return getCreatedAt();
  }

  @Override
  public Byte component16() {
    return getSystemQuarantine();
  }

  @Override
  public Long value1() {
    return getId();
  }

  @Override
  public String value2() {
    return getResourceType();
  }

  @Override
  public Integer value3() {
    return getResourceId();
  }

  @Override
  public Integer value4() {
    return getDatasetId();
  }

  @Override
  public String value5() {
    return getName();
  }

  @Override
  public String value6() {
    return getBucket();
  }

  @Override
  public Long value7() {
    return getSizeValue();
  }

  @Override
  public Long value8() {
    return getRecordCount();
  }

  @Override
  public Integer value9() {
    return getDataCredentialsId();
  }

  @Override
  public String value10() {
    return getCronFrequency();
  }

  @Override
  public Integer value11() {
    return getQuarantineSettingId();
  }

  @Override
  public Integer value12() {
    return getOrgId();
  }

  @Override
  public Integer value13() {
    return getOwnerId();
  }

  @Override
  public LocalDateTime value14() {
    return getUpdatedAt();
  }

  @Override
  public LocalDateTime value15() {
    return getCreatedAt();
  }

  @Override
  public Byte value16() {
    return getSystemQuarantine();
  }

  @Override
  public QuarantineFilesRecord value1(Long value) {
    setId(value);
    return this;
  }

  @Override
  public QuarantineFilesRecord value2(String value) {
    setResourceType(value);
    return this;
  }

  @Override
  public QuarantineFilesRecord value3(Integer value) {
    setResourceId(value);
    return this;
  }

  @Override
  public QuarantineFilesRecord value4(Integer value) {
    setDatasetId(value);
    return this;
  }

  @Override
  public QuarantineFilesRecord value5(String value) {
    setName(value);
    return this;
  }

  @Override
  public QuarantineFilesRecord value6(String value) {
    setBucket(value);
    return this;
  }

  @Override
  public QuarantineFilesRecord value7(Long value) {
    setSizeValue(value);
    return this;
  }

  @Override
  public QuarantineFilesRecord value8(Long value) {
    setRecordCount(value);
    return this;
  }

  @Override
  public QuarantineFilesRecord value9(Integer value) {
    setDataCredentialsId(value);
    return this;
  }

  @Override
  public QuarantineFilesRecord value10(String value) {
    setCronFrequency(value);
    return this;
  }

  @Override
  public QuarantineFilesRecord value11(Integer value) {
    setQuarantineSettingId(value);
    return this;
  }

  @Override
  public QuarantineFilesRecord value12(Integer value) {
    setOrgId(value);
    return this;
  }

  @Override
  public QuarantineFilesRecord value13(Integer value) {
    setOwnerId(value);
    return this;
  }

  @Override
  public QuarantineFilesRecord value14(LocalDateTime value) {
    setUpdatedAt(value);
    return this;
  }

  @Override
  public QuarantineFilesRecord value15(LocalDateTime value) {
    setCreatedAt(value);
    return this;
  }

  @Override
  public QuarantineFilesRecord value16(Byte value) {
    setSystemQuarantine(value);
    return this;
  }

  @Override
  public QuarantineFilesRecord values(
      Long value1,
      String value2,
      Integer value3,
      Integer value4,
      String value5,
      String value6,
      Long value7,
      Long value8,
      Integer value9,
      String value10,
      Integer value11,
      Integer value12,
      Integer value13,
      LocalDateTime value14,
      LocalDateTime value15,
      Byte value16) {
    value1(value1);
    value2(value2);
    value3(value3);
    value4(value4);
    value5(value5);
    value6(value6);
    value7(value7);
    value8(value8);
    value9(value9);
    value10(value10);
    value11(value11);
    value12(value12);
    value13(value13);
    value14(value14);
    value15(value15);
    value16(value16);
    return this;
  }

  // -------------------------------------------------------------------------
  // Constructors
  // -------------------------------------------------------------------------

  /** Create a detached QuarantineFilesRecord */
  public QuarantineFilesRecord() {
    super(QuarantineFiles.QUARANTINE_FILES);
  }

  /** Create a detached, initialised QuarantineFilesRecord */
  public QuarantineFilesRecord(
      Long id,
      String resourceType,
      Integer resourceId,
      Integer datasetId,
      String name,
      String bucket,
      Long sizeValue,
      Long recordCount,
      Integer dataCredentialsId,
      String cronFrequency,
      Integer quarantineSettingId,
      Integer orgId,
      Integer ownerId,
      LocalDateTime updatedAt,
      LocalDateTime createdAt,
      Byte systemQuarantine) {
    super(QuarantineFiles.QUARANTINE_FILES);

    setId(id);
    setResourceType(resourceType);
    setResourceId(resourceId);
    setDatasetId(datasetId);
    setName(name);
    setBucket(bucket);
    setSizeValue(sizeValue);
    setRecordCount(recordCount);
    setDataCredentialsId(dataCredentialsId);
    setCronFrequency(cronFrequency);
    setQuarantineSettingId(quarantineSettingId);
    setOrgId(orgId);
    setOwnerId(ownerId);
    setUpdatedAt(updatedAt);
    setCreatedAt(createdAt);
    setSystemQuarantine(systemQuarantine);
  }
}
