/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.monitoring.schema.tables;

import com.nexla.db.monitoring.schema.DefaultSchema;
import com.nexla.db.monitoring.schema.Keys;
import com.nexla.db.monitoring.schema.tables.records.ConnectorStateRecord;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row5;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class ConnectorState extends TableImpl<ConnectorStateRecord> {

  private static final long serialVersionUID = 1L;

  /** The reference instance of <code>connector_state</code> */
  public static final ConnectorState CONNECTOR_STATE = new ConnectorState();

  /** The class holding records for this type */
  @Override
  public Class<ConnectorStateRecord> getRecordType() {
    return ConnectorStateRecord.class;
  }

  /** The column <code>connector_state.resource_type</code>. */
  public final TableField<ConnectorStateRecord, String> RESOURCE_TYPE =
      createField(DSL.name("resource_type"), SQLDataType.VARCHAR(10).nullable(false), this, "");

  /** The column <code>connector_state.resource_id</code>. */
  public final TableField<ConnectorStateRecord, Integer> RESOURCE_ID =
      createField(DSL.name("resource_id"), SQLDataType.INTEGER.nullable(false), this, "");

  /** The column <code>connector_state.state</code>. */
  public final TableField<ConnectorStateRecord, String> STATE =
      createField(DSL.name("state"), SQLDataType.VARCHAR(20).nullable(false), this, "");

  /** The column <code>connector_state.message</code>. */
  public final TableField<ConnectorStateRecord, String> MESSAGE =
      createField(
          DSL.name("message"),
          SQLDataType.CLOB.defaultValue(DSL.inline("NULL", SQLDataType.CLOB)),
          this,
          "");

  /** The column <code>connector_state.last_modified</code>. */
  public final TableField<ConnectorStateRecord, LocalDateTime> LAST_MODIFIED =
      createField(
          DSL.name("last_modified"),
          SQLDataType.LOCALDATETIME(3)
              .nullable(false)
              .defaultValue(DSL.field("current_timestamp(3)", SQLDataType.LOCALDATETIME)),
          this,
          "");

  private ConnectorState(Name alias, Table<ConnectorStateRecord> aliased) {
    this(alias, aliased, null);
  }

  private ConnectorState(Name alias, Table<ConnectorStateRecord> aliased, Field<?>[] parameters) {
    super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
  }

  /** Create an aliased <code>connector_state</code> table reference */
  public ConnectorState(String alias) {
    this(DSL.name(alias), CONNECTOR_STATE);
  }

  /** Create an aliased <code>connector_state</code> table reference */
  public ConnectorState(Name alias) {
    this(alias, CONNECTOR_STATE);
  }

  /** Create a <code>connector_state</code> table reference */
  public ConnectorState() {
    this(DSL.name("connector_state"), null);
  }

  public <O extends Record> ConnectorState(
      Table<O> child, ForeignKey<O, ConnectorStateRecord> key) {
    super(child, key, CONNECTOR_STATE);
  }

  @Override
  public Schema getSchema() {
    return DefaultSchema.DEFAULT_SCHEMA;
  }

  @Override
  public UniqueKey<ConnectorStateRecord> getPrimaryKey() {
    return Keys.KEY_CONNECTOR_STATE_PRIMARY;
  }

  @Override
  public List<UniqueKey<ConnectorStateRecord>> getKeys() {
    return Arrays.<UniqueKey<ConnectorStateRecord>>asList(Keys.KEY_CONNECTOR_STATE_PRIMARY);
  }

  @Override
  public ConnectorState as(String alias) {
    return new ConnectorState(DSL.name(alias), this);
  }

  @Override
  public ConnectorState as(Name alias) {
    return new ConnectorState(alias, this);
  }

  /** Rename this table */
  @Override
  public ConnectorState rename(String name) {
    return new ConnectorState(DSL.name(name), null);
  }

  /** Rename this table */
  @Override
  public ConnectorState rename(Name name) {
    return new ConnectorState(name, null);
  }

  // -------------------------------------------------------------------------
  // Row5 type methods
  // -------------------------------------------------------------------------

  @Override
  public Row5<String, Integer, String, String, LocalDateTime> fieldsRow() {
    return (Row5) super.fieldsRow();
  }
}
