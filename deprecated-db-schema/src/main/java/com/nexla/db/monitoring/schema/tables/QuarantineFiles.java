/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.monitoring.schema.tables;

import com.nexla.db.monitoring.schema.DefaultSchema;
import com.nexla.db.monitoring.schema.Indexes;
import com.nexla.db.monitoring.schema.Keys;
import com.nexla.db.monitoring.schema.tables.records.QuarantineFilesRecord;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row16;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class QuarantineFiles extends TableImpl<QuarantineFilesRecord> {

  private static final long serialVersionUID = 1L;

  /** The reference instance of <code>quarantine_files</code> */
  public static final QuarantineFiles QUARANTINE_FILES = new QuarantineFiles();

  /** The class holding records for this type */
  @Override
  public Class<QuarantineFilesRecord> getRecordType() {
    return QuarantineFilesRecord.class;
  }

  /** The column <code>quarantine_files.id</code>. */
  public final TableField<QuarantineFilesRecord, Long> ID =
      createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "");

  /** The column <code>quarantine_files.resource_type</code>. */
  public final TableField<QuarantineFilesRecord, String> RESOURCE_TYPE =
      createField(DSL.name("resource_type"), SQLDataType.VARCHAR(10).nullable(false), this, "");

  /** The column <code>quarantine_files.resource_id</code>. */
  public final TableField<QuarantineFilesRecord, Integer> RESOURCE_ID =
      createField(DSL.name("resource_id"), SQLDataType.INTEGER.nullable(false), this, "");

  /** The column <code>quarantine_files.dataset_id</code>. */
  public final TableField<QuarantineFilesRecord, Integer> DATASET_ID =
      createField(
          DSL.name("dataset_id"),
          SQLDataType.INTEGER.defaultValue(DSL.inline("NULL", SQLDataType.INTEGER)),
          this,
          "");

  /** The column <code>quarantine_files.name</code>. */
  public final TableField<QuarantineFilesRecord, String> NAME =
      createField(DSL.name("name"), SQLDataType.VARCHAR(500).nullable(false), this, "");

  /** The column <code>quarantine_files.bucket</code>. */
  public final TableField<QuarantineFilesRecord, String> BUCKET =
      createField(
          DSL.name("bucket"),
          SQLDataType.VARCHAR(100).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)),
          this,
          "");

  /** The column <code>quarantine_files.size_value</code>. */
  public final TableField<QuarantineFilesRecord, Long> SIZE_VALUE =
      createField(
          DSL.name("size_value"),
          SQLDataType.BIGINT.defaultValue(DSL.inline("NULL", SQLDataType.BIGINT)),
          this,
          "");

  /** The column <code>quarantine_files.record_count</code>. */
  public final TableField<QuarantineFilesRecord, Long> RECORD_COUNT =
      createField(
          DSL.name("record_count"),
          SQLDataType.BIGINT.defaultValue(DSL.inline("NULL", SQLDataType.BIGINT)),
          this,
          "");

  /** The column <code>quarantine_files.data_credentials_id</code>. */
  public final TableField<QuarantineFilesRecord, Integer> DATA_CREDENTIALS_ID =
      createField(
          DSL.name("data_credentials_id"),
          SQLDataType.INTEGER.defaultValue(DSL.inline("NULL", SQLDataType.INTEGER)),
          this,
          "");

  /** The column <code>quarantine_files.cron_frequency</code>. */
  public final TableField<QuarantineFilesRecord, String> CRON_FREQUENCY =
      createField(DSL.name("cron_frequency"), SQLDataType.VARCHAR(20).nullable(false), this, "");

  /** The column <code>quarantine_files.quarantine_setting_id</code>. */
  public final TableField<QuarantineFilesRecord, Integer> QUARANTINE_SETTING_ID =
      createField(DSL.name("quarantine_setting_id"), SQLDataType.INTEGER.nullable(false), this, "");

  /** The column <code>quarantine_files.org_id</code>. */
  public final TableField<QuarantineFilesRecord, Integer> ORG_ID =
      createField(
          DSL.name("org_id"),
          SQLDataType.INTEGER.defaultValue(DSL.inline("NULL", SQLDataType.INTEGER)),
          this,
          "");

  /** The column <code>quarantine_files.owner_id</code>. */
  public final TableField<QuarantineFilesRecord, Integer> OWNER_ID =
      createField(
          DSL.name("owner_id"),
          SQLDataType.INTEGER.defaultValue(DSL.inline("NULL", SQLDataType.INTEGER)),
          this,
          "");

  /** The column <code>quarantine_files.updated_at</code>. */
  public final TableField<QuarantineFilesRecord, LocalDateTime> UPDATED_AT =
      createField(
          DSL.name("updated_at"),
          SQLDataType.LOCALDATETIME(3).defaultValue(DSL.inline("NULL", SQLDataType.LOCALDATETIME)),
          this,
          "");

  /** The column <code>quarantine_files.created_at</code>. */
  public final TableField<QuarantineFilesRecord, LocalDateTime> CREATED_AT =
      createField(
          DSL.name("created_at"),
          SQLDataType.LOCALDATETIME(3).defaultValue(DSL.inline("NULL", SQLDataType.LOCALDATETIME)),
          this,
          "");

  /** The column <code>quarantine_files.system_quarantine</code>. */
  public final TableField<QuarantineFilesRecord, Byte> SYSTEM_QUARANTINE =
      createField(
          DSL.name("system_quarantine"),
          SQLDataType.TINYINT.defaultValue(DSL.inline("0", SQLDataType.TINYINT)),
          this,
          "");

  private QuarantineFiles(Name alias, Table<QuarantineFilesRecord> aliased) {
    this(alias, aliased, null);
  }

  private QuarantineFiles(Name alias, Table<QuarantineFilesRecord> aliased, Field<?>[] parameters) {
    super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
  }

  /** Create an aliased <code>quarantine_files</code> table reference */
  public QuarantineFiles(String alias) {
    this(DSL.name(alias), QUARANTINE_FILES);
  }

  /** Create an aliased <code>quarantine_files</code> table reference */
  public QuarantineFiles(Name alias) {
    this(alias, QUARANTINE_FILES);
  }

  /** Create a <code>quarantine_files</code> table reference */
  public QuarantineFiles() {
    this(DSL.name("quarantine_files"), null);
  }

  public <O extends Record> QuarantineFiles(
      Table<O> child, ForeignKey<O, QuarantineFilesRecord> key) {
    super(child, key, QUARANTINE_FILES);
  }

  @Override
  public Schema getSchema() {
    return DefaultSchema.DEFAULT_SCHEMA;
  }

  @Override
  public List<Index> getIndexes() {
    return Arrays.<Index>asList(
        Indexes.QUARANTINE_FILES_QUARANTINE_FILES_DATASET_ID,
        Indexes.QUARANTINE_FILES_QUARANTINE_FILES_ORG_ID,
        Indexes.QUARANTINE_FILES_QUARANTINE_FILES_OWNER_ID,
        Indexes.QUARANTINE_FILES_QUARANTINE_FILES_RESOURCE,
        Indexes.QUARANTINE_FILES_QUARANTINE_FILES_RESOURCE_ID,
        Indexes.QUARANTINE_FILES_QUARANTINE_FILES_RESOURCE_TYPE);
  }

  @Override
  public Identity<QuarantineFilesRecord, Long> getIdentity() {
    return (Identity<QuarantineFilesRecord, Long>) super.getIdentity();
  }

  @Override
  public UniqueKey<QuarantineFilesRecord> getPrimaryKey() {
    return Keys.KEY_QUARANTINE_FILES_PRIMARY;
  }

  @Override
  public List<UniqueKey<QuarantineFilesRecord>> getKeys() {
    return Arrays.<UniqueKey<QuarantineFilesRecord>>asList(Keys.KEY_QUARANTINE_FILES_PRIMARY);
  }

  @Override
  public QuarantineFiles as(String alias) {
    return new QuarantineFiles(DSL.name(alias), this);
  }

  @Override
  public QuarantineFiles as(Name alias) {
    return new QuarantineFiles(alias, this);
  }

  /** Rename this table */
  @Override
  public QuarantineFiles rename(String name) {
    return new QuarantineFiles(DSL.name(name), null);
  }

  /** Rename this table */
  @Override
  public QuarantineFiles rename(Name name) {
    return new QuarantineFiles(name, null);
  }

  // -------------------------------------------------------------------------
  // Row16 type methods
  // -------------------------------------------------------------------------

  @Override
  public Row16<
          Long,
          String,
          Integer,
          Integer,
          String,
          String,
          Long,
          Long,
          Integer,
          String,
          Integer,
          Integer,
          Integer,
          LocalDateTime,
          LocalDateTime,
          Byte>
      fieldsRow() {
    return (Row16) super.fieldsRow();
  }
}
