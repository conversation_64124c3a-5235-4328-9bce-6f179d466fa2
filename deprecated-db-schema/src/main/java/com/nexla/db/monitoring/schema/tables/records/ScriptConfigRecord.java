/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.monitoring.schema.tables.records;

import com.nexla.db.monitoring.schema.tables.ScriptConfig;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.UpdatableRecordImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class ScriptConfigRecord extends UpdatableRecordImpl<ScriptConfigRecord>
    implements Record7<Long, String, String, String, String, String, Integer> {

  private static final long serialVersionUID = 1L;

  /** Setter for <code>script_config.id</code>. */
  public void setId(Long value) {
    set(0, value);
  }

  /** Getter for <code>script_config.id</code>. */
  public Long getId() {
    return (Long) get(0);
  }

  /** Setter for <code>script_config.cron</code>. */
  public void setCron(String value) {
    set(1, value);
  }

  /** Getter for <code>script_config.cron</code>. */
  public String getCron() {
    return (String) get(1);
  }

  /** Setter for <code>script_config.script_github_path</code>. */
  public void setScriptGithubPath(String value) {
    set(2, value);
  }

  /** Getter for <code>script_config.script_github_path</code>. */
  public String getScriptGithubPath() {
    return (String) get(2);
  }

  /** Setter for <code>script_config.parameters</code>. */
  public void setParameters(String value) {
    set(3, value);
  }

  /** Getter for <code>script_config.parameters</code>. */
  public String getParameters() {
    return (String) get(3);
  }

  /** Setter for <code>script_config.description</code>. */
  public void setDescription(String value) {
    set(4, value);
  }

  /** Getter for <code>script_config.description</code>. */
  public String getDescription() {
    return (String) get(4);
  }

  /** Setter for <code>script_config.status</code>. */
  public void setStatus(String value) {
    set(5, value);
  }

  /** Getter for <code>script_config.status</code>. */
  public String getStatus() {
    return (String) get(5);
  }

  /** Setter for <code>script_config.source_id</code>. */
  public void setSourceId(Integer value) {
    set(6, value);
  }

  /** Getter for <code>script_config.source_id</code>. */
  public Integer getSourceId() {
    return (Integer) get(6);
  }

  // -------------------------------------------------------------------------
  // Primary key information
  // -------------------------------------------------------------------------

  @Override
  public Record1<Long> key() {
    return (Record1) super.key();
  }

  // -------------------------------------------------------------------------
  // Record7 type implementation
  // -------------------------------------------------------------------------

  @Override
  public Row7<Long, String, String, String, String, String, Integer> fieldsRow() {
    return (Row7) super.fieldsRow();
  }

  @Override
  public Row7<Long, String, String, String, String, String, Integer> valuesRow() {
    return (Row7) super.valuesRow();
  }

  @Override
  public Field<Long> field1() {
    return ScriptConfig.SCRIPT_CONFIG.ID;
  }

  @Override
  public Field<String> field2() {
    return ScriptConfig.SCRIPT_CONFIG.CRON;
  }

  @Override
  public Field<String> field3() {
    return ScriptConfig.SCRIPT_CONFIG.SCRIPT_GITHUB_PATH;
  }

  @Override
  public Field<String> field4() {
    return ScriptConfig.SCRIPT_CONFIG.PARAMETERS;
  }

  @Override
  public Field<String> field5() {
    return ScriptConfig.SCRIPT_CONFIG.DESCRIPTION;
  }

  @Override
  public Field<String> field6() {
    return ScriptConfig.SCRIPT_CONFIG.STATUS;
  }

  @Override
  public Field<Integer> field7() {
    return ScriptConfig.SCRIPT_CONFIG.SOURCE_ID;
  }

  @Override
  public Long component1() {
    return getId();
  }

  @Override
  public String component2() {
    return getCron();
  }

  @Override
  public String component3() {
    return getScriptGithubPath();
  }

  @Override
  public String component4() {
    return getParameters();
  }

  @Override
  public String component5() {
    return getDescription();
  }

  @Override
  public String component6() {
    return getStatus();
  }

  @Override
  public Integer component7() {
    return getSourceId();
  }

  @Override
  public Long value1() {
    return getId();
  }

  @Override
  public String value2() {
    return getCron();
  }

  @Override
  public String value3() {
    return getScriptGithubPath();
  }

  @Override
  public String value4() {
    return getParameters();
  }

  @Override
  public String value5() {
    return getDescription();
  }

  @Override
  public String value6() {
    return getStatus();
  }

  @Override
  public Integer value7() {
    return getSourceId();
  }

  @Override
  public ScriptConfigRecord value1(Long value) {
    setId(value);
    return this;
  }

  @Override
  public ScriptConfigRecord value2(String value) {
    setCron(value);
    return this;
  }

  @Override
  public ScriptConfigRecord value3(String value) {
    setScriptGithubPath(value);
    return this;
  }

  @Override
  public ScriptConfigRecord value4(String value) {
    setParameters(value);
    return this;
  }

  @Override
  public ScriptConfigRecord value5(String value) {
    setDescription(value);
    return this;
  }

  @Override
  public ScriptConfigRecord value6(String value) {
    setStatus(value);
    return this;
  }

  @Override
  public ScriptConfigRecord value7(Integer value) {
    setSourceId(value);
    return this;
  }

  @Override
  public ScriptConfigRecord values(
      Long value1,
      String value2,
      String value3,
      String value4,
      String value5,
      String value6,
      Integer value7) {
    value1(value1);
    value2(value2);
    value3(value3);
    value4(value4);
    value5(value5);
    value6(value6);
    value7(value7);
    return this;
  }

  // -------------------------------------------------------------------------
  // Constructors
  // -------------------------------------------------------------------------

  /** Create a detached ScriptConfigRecord */
  public ScriptConfigRecord() {
    super(ScriptConfig.SCRIPT_CONFIG);
  }

  /** Create a detached, initialised ScriptConfigRecord */
  public ScriptConfigRecord(
      Long id,
      String cron,
      String scriptGithubPath,
      String parameters,
      String description,
      String status,
      Integer sourceId) {
    super(ScriptConfig.SCRIPT_CONFIG);

    setId(id);
    setCron(cron);
    setScriptGithubPath(scriptGithubPath);
    setParameters(parameters);
    setDescription(description);
    setStatus(status);
    setSourceId(sourceId);
  }
}
