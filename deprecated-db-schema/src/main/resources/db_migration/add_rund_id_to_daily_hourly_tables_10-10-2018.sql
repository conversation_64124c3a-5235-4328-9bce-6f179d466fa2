ALTER TABLE data_sources_hourly ADD run_id BIGINT NULL;
CREATE INDEX run_id ON data_sources_hourly (run_id);

ALTER TABLE data_sources_daily ADD run_id BIGINT NULL;
CREATE INDEX run_id ON data_sources_daily (run_id);

ALTER TABLE data_sinks_hourly ADD run_id BIGINT NULL;
CREATE INDEX run_id ON data_sinks_hourly (run_id);

ALTER TABLE data_sinks_daily ADD run_id BIGINT NULL;
CREATE INDEX run_id ON data_sinks_daily (run_id);

ALTER TABLE data_pubs_hourly ADD run_id BIGINT NULL;
CREATE INDEX run_id ON data_pubs_hourly (run_id);

ALTER TABLE data_pubs_daily ADD run_id BIGINT NULL;
CREATE INDEX run_id ON data_pubs_daily (run_id);

ALTER TABLE data_subs_hourly ADD run_id BIGINT NULL;
CREATE INDEX run_id ON data_subs_hourly (run_id);

ALTER TABLE data_subs_daily ADD run_id BIGINT NULL;
CREATE INDEX run_id ON data_subs_daily (run_id);


ALTER TABLE data_sources_hourly DROP INDEX data_sources_hourly_id, ADD UNIQUE KEY `data_sources_hourly_id` (resource_id ASC, data_set_id, reporting_hour ASC, run_id);

ALTER TABLE data_sources_daily DROP INDEX data_sources_daily_id, ADD UNIQUE KEY `data_sources_daily_id` (resource_id ASC, data_set_id, reporting_date ASC, run_id);

ALTER TABLE data_sinks_hourly DROP INDEX data_sinks_hourly_id, ADD UNIQUE KEY `data_sinks_hourly_id` (resource_id ASC, data_set_id, reporting_hour ASC, run_id);

ALTER TABLE data_sinks_daily DROP INDEX data_sinks_daily_id, ADD UNIQUE KEY `data_sinks_daily_id` (resource_id ASC, data_set_id, reporting_date ASC, run_id);

ALTER TABLE data_pubs_hourly DROP INDEX data_pubs_hourly_id, ADD UNIQUE KEY `data_pubs_hourly_id` (resource_id ASC, reporting_hour ASC, data_set_id ASC, run_id);

ALTER TABLE data_pubs_daily DROP INDEX data_pubs_daily_id, ADD UNIQUE KEY `data_pubs_daily_id` (resource_id ASC, reporting_date ASC, data_set_id ASC, run_id);

ALTER TABLE data_subs_hourly DROP INDEX data_subs_hourly_id, ADD UNIQUE KEY `data_subs_hourly_id` (resource_id ASC, reporting_hour ASC, data_set_id ASC, run_id);

ALTER TABLE data_subs_daily DROP INDEX data_subs_daily_id, ADD UNIQUE KEY `data_subs_daily_id` (resource_id ASC, reporting_date ASC, data_set_id ASC, run_id);

