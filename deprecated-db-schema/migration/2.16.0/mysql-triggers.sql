DROP TRIGGER IF EXISTS pipeline_task_update;
DROP TRIGGER IF EXISTS pipeline_task_insert;
DROP TRIGGER IF EXISTS pipeline_task_delete;
DROP TABLE IF EXISTS `audit_pipeline_task`;

CREATE TABLE `audit_pipeline_task` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `action` varchar(10) NOT NULL,
  `audit_ts` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `task_id` varchar(256) DEFAULT NULL,
  `task_type` varchar(36) DEFAULT NULL,
  `max_instances` int DEFAULT NULL,
  `state` varchar(36) DEFAULT NULL,
  `meta` text,
  PRIMARY KEY (`id`, `audit_ts`)
) PARTITION BY RANGE ( UNIX_TIMESTAMP(audit_ts)) (
    PARTITION p2 VALUES LESS THAN (UNIX_TIMESTAMP('2024-02-01 00:00:00')),
    PARTITION p3 VALUES LESS THAN (UNIX_TIMESTAMP('2024-02-15 00:00:00')),
    PARTITION p4 VALUES LESS THAN (UNIX_TIMESTAMP('2024-03-01 00:00:00')),
    PARTITION p5 VALUES LESS THAN (UNIX_TIMESTAMP('2024-03-15 00:00:00')),
    PARTITION p6 VALUES LESS THAN (UNIX_TIMESTAMP('2024-04-01 00:00:00')),
    PARTITION p7 VALUES LESS THAN (UNIX_TIMESTAMP('2024-04-15 00:00:00')),
    PARTITION p8 VALUES LESS THAN (UNIX_TIMESTAMP('2024-05-01 00:00:00')),
    PARTITION p9 VALUES LESS THAN (UNIX_TIMESTAMP('2024-05-15 00:00:00')),
    PARTITION p10 VALUES LESS THAN (UNIX_TIMESTAMP('2024-06-01 00:00:00')),
    PARTITION p11 VALUES LESS THAN (UNIX_TIMESTAMP('2024-06-15 00:00:00')),
    PARTITION p12 VALUES LESS THAN (UNIX_TIMESTAMP('2024-07-01 00:00:00')),
    PARTITION p13 VALUES LESS THAN (UNIX_TIMESTAMP('2024-07-15 00:00:00')),
    PARTITION p14 VALUES LESS THAN (UNIX_TIMESTAMP('2024-08-01 00:00:00')),
    PARTITION p15 VALUES LESS THAN (UNIX_TIMESTAMP('2024-08-15 00:00:00')),
    PARTITION p16 VALUES LESS THAN (UNIX_TIMESTAMP('2024-09-01 00:00:00')),
    PARTITION p17 VALUES LESS THAN (UNIX_TIMESTAMP('2024-09-15 00:00:00')),
    PARTITION p18 VALUES LESS THAN (UNIX_TIMESTAMP('2024-10-01 00:00:00')),
    PARTITION p19 VALUES LESS THAN (UNIX_TIMESTAMP('2024-10-15 00:00:00')),
    PARTITION p20 VALUES LESS THAN (UNIX_TIMESTAMP('2024-11-01 00:00:00')),
    PARTITION p21 VALUES LESS THAN (UNIX_TIMESTAMP('2024-11-15 00:00:00')),
    PARTITION p22 VALUES LESS THAN (UNIX_TIMESTAMP('2024-12-01 00:00:00')),
    PARTITION p23 VALUES LESS THAN (UNIX_TIMESTAMP('2024-12-15 00:00:00')),
    PARTITION p24 VALUES LESS THAN (UNIX_TIMESTAMP('2025-01-01 00:00:00')),
    PARTITION pN VALUES LESS THAN MAXVALUE
);


CREATE TRIGGER pipeline_task_insert
  AFTER INSERT ON pipeline_task FOR EACH ROW
  INSERT INTO audit_pipeline_task(`action`, audit_ts, task_id, task_type, max_instances, state, meta)
        VALUES('insert', CURRENT_TIMESTAMP, new.task_id, new.task_type, new.max_instances, new.state, new.meta);

DELIMITER $$

CREATE TRIGGER pipeline_task_update AFTER UPDATE ON pipeline_task
FOR EACH ROW
BEGIN
  IF (old.max_instances <> new.max_instances OR old.meta <> new.meta) THEN
      INSERT INTO audit_pipeline_task(`action`, audit_ts, task_id, task_type, max_instances, state, meta) VALUES('update', CURRENT_TIMESTAMP, new.task_id, new.task_type, new.max_instances, new.state, new.meta);
  END IF;
END$$

DELIMITER ;


CREATE TRIGGER pipeline_task_delete
  AFTER DELETE ON pipeline_task FOR EACH ROW
  INSERT INTO audit_pipeline_task(`action`, audit_ts, task_id, task_type, max_instances, state, meta)
        VALUES('delete', CURRENT_TIMESTAMP, old.task_id, old.task_type, old.max_instances, old.state, old.meta);

