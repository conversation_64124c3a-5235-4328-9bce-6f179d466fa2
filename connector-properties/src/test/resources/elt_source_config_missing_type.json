{"unit.test": "true", "source_id": "1", "pipeline.type": "elt", "start.cron": "0 15 20 8 1 ? 2025", "common": {"sequence": "parallel", "max_parallel_feeds": 100, "order_first": [], "order_last": [], "feed_params": {}}, "feeds": {"base_full": {"type": "base", "spec": {"rest.iterations": [{"key": "step1", "method": "GET", "date.format": "yyyy-MM-dd'T'HH:mm:ss'Z'", "date.time.unit": "dd", "url.template": "{data_credential[\"oauth2.instance_url\"]}/services/data/v52.0/query/?q=SELECT+QualifiedApiName,+EntityDefinition.QualifiedApiName,+DataType,+Label+FROM+EntityParticle+WHERE+EntityDefinition.QualifiedApiName+=+'{feed_spec.object_name}'+AND+EntityDefinition.IsDeprecatedAndHidden+=+false", "iteration.type": "static.url", "response.data.path": "$"}, {"key": "step2", "url.template": "{data_credential[\"oauth2.instance_url\"]}/services/data/v52.0/query/?q=SELECT+QualifiedApiName,+EntityDefinition.QualifiedApiName,+DataType,+Label+FROM+EntityParticle+WHERE+EntityDefinition.QualifiedApiName+='{feed_spec.object_name}'+AND+EntityDefinition.IsDeprecatedAndHidden+=+false", "iteration.type": "code.container", "code.container.id": 13602}, {"key": "step3", "date.format": "yyyy-MM-dd'T'HH:mm:ss'Z'", "date.time.unit": "dd", "method": "GET", "url.template": "{data_credential[\"oauth2.instance_url\"]}/services/data/v52.0/query/?q={step2.query_start}+FROM+{step2.query_obj}", "iteration.type": "paging.next.url", "response.data.path": "$.records[*]", "response.next.url.data.path": "$.nextRecordsUrl"}]}}, "base_incremental": {"type": "base", "spec": {"rest.iterations": [{"key": "step1", "method": "GET", "date.format": "yyyy-MM-dd'T'HH:mm:ss'Z'", "date.time.unit": "dd", "url.template": "{data_credential[\"oauth2.instance_url\"]}/services/data/v52.0/query/?q=SELECT+QualifiedApiName,+EntityDefinition.QualifiedApiName,+DataType,+Label+FROM+EntityParticle+WHERE+EntityDefinition.QualifiedApiName+=+'{feed_spec.object_name}'+AND+EntityDefinition.IsDeprecatedAndHidden+=+false", "iteration.type": "static.url", "response.data.path": "$"}, {"key": "step2", "url.template": "{data_credential[\"oauth2.instance_url\"]}/services/data/v52.0/query/?q=SELECT+QualifiedApiName,+EntityDefinition.QualifiedApiName,+DataType,+Label+FROM+EntityParticle+WHERE+EntityDefinition.QualifiedApiName+='{feed_spec.object_name}'+AND+EntityDefinition.IsDeprecatedAndHidden+=+false", "iteration.type": "code.container", "code.container.id": 13602}, {"key": "step3", "date.format": "yyyy-MM-dd'T'HH:mm:ss'Z'", "date.time.unit": "dd", "method": "GET", "url.template": "{data_credential[\"oauth2.instance_url\"]}/services/data/v52.0/query/?q={step2.query_start}+FROM+{step2.query_obj}+WHERE+{step2.query_incremental_col}<{now}+and+{step2.query_incremental_col}>{now-1}", "iteration.type": "paging.next.url", "response.data.path": "$.records[*]", "response.next.url.data.path": "$.nextRecordsUrl"}]}}, "AIApplication": {"type": "data", "spec": {"ref_spec": "base_full", "primary.keys": ["Id"], "params": {"feed_spec.object_name": "AIApplication", "ui.feed_display_name": "AI Application"}}}, "AIApplicationConfig": {"spec": {"ref_spec": "base_full", "primary.keys": ["Id"], "params": {"feed_spec.object_name": "AIApplicationConfig", "ui.feed_display_name": "AI Application config"}}}, "AcceptedEventRelation": {"spec": {"primary.keys": ["Id"], "params": {"feed_spec.object_name": "AcceptedEventRelation", "ui.feed_display_name": "Accepted Event Relation"}}}}}