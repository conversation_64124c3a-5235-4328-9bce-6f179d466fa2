package com.nexla.spec

import com.nexla.common.connectiontype.{ConfigSpecDto, LoadConnectorServiceClient}
import com.nexla.common.{ConnectionType, ResourceType}
import com.nexla.connector.config.rest.BaseAuthConfig
import com.nexla.connector.config.{BaseConnectorConfig, NexlaConfigDef}
import com.nexla.spec.Connectors.configs
import org.apache.kafka.common.config.ConfigDef
import org.slf4j.LoggerFactory

import java.util
import scala.collection.JavaConverters._
import scala.compat.java8.OptionConverters.RichOptionalGeneric
import scala.util.Try

object Configs {

  private val LOGGER = LoggerFactory.getLogger(getClass)

  private val byResourceAndConnectorType: Map[(ResourceType, ConnectionType), ConfigSpec] = configs
    .filterNot(_.isSecondary)
    .flatMap(c =>
      c.sourceConfig.map(conf => ((ResourceType.SOURCE, c.connectionType), conf)) ++
        c.sinkConfig.map(conf => ((ResourceType.SINK, c.connectionType), conf)))
    .toMap

  private val specByConnectorType: Map[ConnectionType, ConnectorSpec] = configs
    .filterNot(_.isSecondary)
    .groupBy(_.connectionType)
    .mapValues(_.head)

  private val byConnectorClass: Map[String, ConfigSpec] = configs
    .flatMap(c => c.sourceConfig ++ c.sinkConfig)
    .map(c => (c.connectorClass, c))
    .toMap

  lazy val remoteClassLoader = LoadConnectorServiceClient.remoteClassLoader()

  def findConfigDef(connectorClass: String): ConfigDef = {
    byConnectorClass.get(connectorClass)
      .orElse(remoteByConnectorClass.get(connectorClass))
      .get
      .config
  }

  def find(resourceType: ResourceType, connectionType: ConnectionType): ConfigSpec = {
    val key = (resourceType, connectionType)
    byResourceAndConnectorType.get(key)
      .orElse(remoteByResourceAndConnectorType.get(key))
      .get
  }

  def findConfigDef(resourceType: ResourceType, connectionType: ConnectionType): ConfigDef = {
    val key = (resourceType, connectionType)
    byResourceAndConnectorType.get(key)
      .orElse(remoteByResourceAndConnectorType.get(key))
      .get
      .config
  }

  def isConnector(resourceType: ResourceType, connectionType: ConnectionType): Boolean = {
    val key = (resourceType, connectionType)
    byResourceAndConnectorType.contains(key) || remoteByResourceAndConnectorType.contains(key)
  }

  def findConnectorClass(resourceType: ResourceType, connectionType: ConnectionType): String = {
    val key = (resourceType, connectionType)
    byResourceAndConnectorType.get(key)
      .orElse(remoteByResourceAndConnectorType.get(key))
      .get
      .connectorClass
  }

  def findSpec(connectionType: ConnectionType): ConnectorSpec = {
    specByConnectorType.get(connectionType)
      .orElse(remoteSpecByConnectorType.get(connectionType))
      .get
  }

  def findSpecOpt(connectionType: ConnectionType): Option[ConnectorSpec] = {
    specByConnectorType.get(connectionType)
      .orElse(remoteSpecByConnectorType.get(connectionType))
  }

  def authConfigCreator(sourceType: ConnectionType, creds: util.Map[String, _], credsId: Integer): BaseAuthConfig = {
    val clazz = Configs.findSpecOpt(sourceType).get.authConfigClass.get
    val constructor = clazz.getConstructor(classOf[java.util.Map[_, _]], classOf[Integer])
    val instance = constructor.newInstance(creds, credsId)
    instance
  }

  private def convertConfigSpec(configSpec: ConfigSpecDto) = {
    val configClass = remoteClassLoader.loadClass(configSpec.configClass).asInstanceOf[Class[BaseConnectorConfig]]
    val method = configClass.getMethod("configDef")
    val configDef = method.invoke(null).asInstanceOf[ConfigDef]
    val value = ConfigSpec(configSpec.connectorClass, configClass, configSpec.dockerImage, configDef, List.empty)
    value
  }

  private def remoteByConnectorClass = {
    val remote = LoadConnectorServiceClient.getConnectorSpecs.asScala
    remote
      .flatMap(x => x.sinkConfig.asScala ++ x.sourceConfig.asScala)
      .map { configSpec =>
        configSpec.configClass -> convertConfigSpec(configSpec)
      }
      .toMap
  }

  private def remoteByResourceAndConnectorType = {
    val remote = LoadConnectorServiceClient.getConnectorSpecs.asScala
    remote.flatMap { connectorSpec =>
      val sourceVal = connectorSpec.sourceConfig.asScala.map { configSpec =>
        ((ResourceType.SOURCE, connectorSpec.connectionType), convertConfigSpec(configSpec))
      }
      val sinkVal = connectorSpec.sinkConfig.asScala.map { configSpec =>
        ((ResourceType.SINK, connectorSpec.connectionType), convertConfigSpec(configSpec))
      }
      sourceVal ++ sinkVal
    }.toMap
  }

  private def remoteSpecByConnectorType = {
    val remote = LoadConnectorServiceClient.getConnectorSpecs.asScala
    remote
      .groupBy(_.connectionType)
      .mapValues(_.head)
      .flatMap { case (k, v) =>
        val authConfigClass = remoteClassLoader.loadClass(v.authConfigClass)
        Try {
          val method = authConfigClass.getMethod("authConfigDef")
          val configDef = method.invoke(null).asInstanceOf[NexlaConfigDef]
          val sourceConfig = v.sourceConfig.asScala.map(convertConfigSpec)
          val sinkConfig = v.sinkConfig.asScala.map(convertConfigSpec)
          val authConfigClassVal = Some(authConfigClass.asInstanceOf[Class[BaseAuthConfig]])
          k -> ConnectorSpec(Some(configDef), authConfigClassVal, sourceConfig, sinkConfig, Map.empty, v.connectionType, v.connectorServiceClass.asScala)
        } fold(exc => {
          LOGGER.error(s"Can't parse Spec for $authConfigClass. Check is authConfigDef method present", exc)
          None
        }, Some(_))
      }
  }

}

case class ConfigSpec(connectorClass: String,
                      configClass: Class[_ <: BaseConnectorConfig],
                      dockerImage: String,
                      config: ConfigDef,
                      groups: List[String] = List.empty)

case class ConnectorSpec(authConfig: Option[ConfigDef] = None,
                         authConfigClass: Option[Class[_ <: BaseAuthConfig]],
                         sourceConfig: Option[ConfigSpec],
                         sinkConfig: Option[ConfigSpec] = None,
                         meta: Map[String, String] = Map.empty,
                         connectionType: ConnectionType,
                         connectorServiceClass: Option[String] = None,
                         isSecondary: Boolean = false)
