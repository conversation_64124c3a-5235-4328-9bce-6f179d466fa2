package com.nexla.connector.config.jdbc.column;

import com.nexla.common.ConnectionType;

import static com.nexla.common.ConnectionType.REDSHIFT;

public interface ColumnNormalizer {

	String toStorage(String column);

	String fromStorage(String column);

	static ColumnNormalizer getInstance(ConnectionType connectionType) {
		if (connectionType.equals(REDSHIFT)) {
			return RedShiftColumnNormalizer.INSTANCE;
		}
		return NoopColumnNormalizer.INSTANCE;
	}
	
}
