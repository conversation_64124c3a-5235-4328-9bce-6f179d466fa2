package com.nexla.connector.file.source;

import com.nexla.admin.client.AdminApiClient;

import java.util.List;
import java.util.Queue;

public interface ITransportFileReader {
    <T> ReadBatchResult<T> readNextBatch(FileReadResult<T> consumer, AdminApiClient adminApiClient);

    void stop();

    Queue<TransportFile> getFileObjects();

    void setCustomParserEnabled(boolean isCustomParserEnabled);

    void addFiles(List<TransportFile> transportFiles);
}
