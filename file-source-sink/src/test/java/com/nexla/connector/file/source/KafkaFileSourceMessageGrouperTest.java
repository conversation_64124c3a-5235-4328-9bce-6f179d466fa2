package com.nexla.connector.file.source;

import static com.nexla.common.NexlaConstants.CREDENTIALS_TYPE;
import static com.nexla.common.NexlaConstants.PATH;
import static com.nexla.common.NexlaConstants.SOURCE_ID;
import static com.nexla.common.NexlaConstants.SOURCE_TYPE;
import static com.nexla.connector.ConnectorService.UNIT_TEST;

import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaMessage;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.connector.config.file.GroupingProps;
import com.nexla.parser.JsonParser;
import com.nexla.test.UnitTests;
import java.io.File;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.junit.Test;
import org.junit.experimental.categories.Category;
import org.junit.jupiter.api.Assertions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Category(UnitTests.class)
public class KafkaFileSourceMessageGrouperTest {

  final static Logger logger = LoggerFactory.getLogger(KafkaFileSourceMessageGrouperTest.class);

  @Test
  public void shouldPreserveNullValues() {
    final KafkaFileSourceMessageGrouper messageGrouper = new KafkaFileSourceMessageGrouper(
        new FileSourceContext(
            new FileSourceConnectorConfig(getConfigMap()),
            Optional.of(1), 1L
        ),
        new NexlaLogger(logger), 1L
    );

    JsonParser parser = new JsonParser();
    StreamEx<Optional<NexlaMessage>> messages = parser.parseMessages(
        this.getClass().getClassLoader().getResourceAsStream("includes_null_values.json"));
    List<LinkedHashMap<String, Object>> rawMessages =
        messages
          .filter(Optional::isPresent)
          .map(m -> m.get().getRawMessage())
        .collect(Collectors.toList());

    LinkedHashMap<String, Object> grouped = messageGrouper.getGroupMessage(
        new GroupingProps(
            List.of("timestamp_type"),
            "grouped_timestamp_type",
            false, true),
        rawMessages
    );

    ArrayList<LinkedHashMap<String, Object>> innerValue =
        ((ArrayList<LinkedHashMap<String, Object>>) grouped.get("grouped_timestamp_type"));
    ArrayList<LinkedHashMap<String, Object>> innerInnerValue =
        (ArrayList<LinkedHashMap<String, Object>>) innerValue.get(0).get("value");

    Assertions.assertNull(innerInnerValue.get(0).get("null_values"));
    Assertions.assertNull(innerInnerValue.get(1).get("null_values"));
    Assertions.assertNull(innerInnerValue.get(2).get("null_values"));
  }

  @SneakyThrows
  private Map<String, String> getConfigMap() {
    return Map.of(
        UNIT_TEST, Boolean.TRUE.toString(),
        SOURCE_ID, "1",
        SOURCE_TYPE, ConnectionType.FTP.name(),
        CREDENTIALS_TYPE, ConnectionType.FTP.name(),
        PATH, new File(this.getClass().getClassLoader().getResource("includes_null_values.json").getFile()).getAbsolutePath());
  }
}
