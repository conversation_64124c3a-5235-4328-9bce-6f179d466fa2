package com.nexla.redis;

import static com.nexla.redis.dto.MasterEntry.DataModelVersion.V1;
import static org.junit.jupiter.api.Assertions.*;

import com.nexla.admin.client.DataMap;
import com.nexla.common.exception.LimitReachedException;
import com.nexla.common.exception.ValidationException;
import com.nexla.redis.dto.MasterEntry;
import io.lettuce.core.ScanArgs;
import java.util.*;
import java.util.stream.Stream;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

@Slf4j
public abstract class RedisLookupTest extends RedisDataMapBaseTest {

  protected static MasterEntry.DataModelVersion DATA_MODEL_VERSION = V1;

  private static DataMap dataMap(
      int id,
      Boolean useVersioning,
      Boolean emitDefaults,
      List<Map<String, String>> data,
      Map<String, String> defaults) {
    DataMap dm = new DataMap();
    dm.setId(id);
    dm.setMapPrimaryKey("UID");
    dm.setUseVersioning(useVersioning);
    dm.setEmitDataDefault(emitDefaults);
    dm.setDataMap(data);
    dm.setDataDefaults(defaults);
    dm.setDataModelVersion(DATA_MODEL_VERSION.toString());
    return dm;
  }

  private static final Boolean WITH_VERSIONING = true;
  private static final Boolean WITHOUT_VERSIONING = false;
  private static final Boolean EMIT_DEFAULTS = true;
  private static final Boolean DONT_EMIT_DEFAULTS = false;

  public static Stream<Arguments> createTestCases() {
    return Stream.of(
        Arguments.of(dataMap(1001, WITH_VERSIONING, EMIT_DEFAULTS, List.of(), Map.of())),
        Arguments.of(
            dataMap(
                1002,
                WITH_VERSIONING,
                EMIT_DEFAULTS,
                List.of(Map.of("UID", "1", "val", "A"), Map.of("UID", "2", "val", "B")),
                Map.of())),
        Arguments.of(
            dataMap(
                1003,
                WITH_VERSIONING,
                EMIT_DEFAULTS,
                List.of(
                    Map.of("UID", "1", "val", "A"),
                    Map.of("UID", "2", "val", "B"),
                    Map.of("UID", "3")),
                Map.of("val", "X"))),
        Arguments.of(dataMap(1004, WITHOUT_VERSIONING, EMIT_DEFAULTS, List.of(), Map.of())),
        Arguments.of(
            dataMap(
                1005,
                WITHOUT_VERSIONING,
                EMIT_DEFAULTS,
                List.of(Map.of("UID", "1", "val", "A"), Map.of("UID", "2")),
                Map.of("val", "X"))),
        Arguments.of(
            dataMap(
                1006,
                WITH_VERSIONING,
                DONT_EMIT_DEFAULTS,
                List.of(
                    Map.of("UID", "1", "val", "A"),
                    Map.of("UID", "2", "val", "B"),
                    Map.of("UID", "3")),
                Map.of())),
        Arguments.of(
            dataMap(
                1007,
                null,
                null,
                List.of(Map.of("UID", "1", "val", "A"), Map.of("UID", "2")),
                Map.of())));
  }

  @ParameterizedTest
  @MethodSource("createTestCases")
  @SneakyThrows
  public void testCreate(DataMap dm) {
    Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, dm.getId());
    try {
      lookup.save(dm);
      MasterEntry master = lookup.getMasterEntry();
      assertEquals("1", master.currentVersion);
      assertNull(master.nextVersion);
      assertEquals(dm.getMapPrimaryKey(), master.mapPrimaryKey);
      assertEquals(dm.getDataMap().size(), master.mapSize);
      assertFalse(master.limitReached);

      assertEquals(dm.getDataMap().size(), lookup.countEntries("1", master.dataModelVersion));
      for (Map<String, String> dmEntry : dm.getDataMap()) {
        Map<String, String> entry = lookup.getEntry("1", dmEntry.get("UID"));
        assertEquals(dmEntry.get("val"), entry.get("val"));
      }
      int expectedDefaultEntryCount = dm.getDataDefaults().isEmpty() ? 0 : 1;
      int expectedEntriesIndexCount =
          master.dataModelVersion == V1 || dm.getDataMap().isEmpty() ? 0 : 1;
      // master entry key + entries index key (optional) + default entries key (optional) + entries
      int expectedRedisKeysCount =
          1 + expectedEntriesIndexCount + expectedDefaultEntryCount + dm.getDataMap().size();
      assertEquals(expectedRedisKeysCount, countMatching(dm.getId()));
    } finally {
      lookup.deleteAsync().get();
    }
  }

  public static Stream<Arguments> updateTestCases() {
    return Stream.of(
        // update with all new keys
        Arguments.of(
            1031,
            WITH_VERSIONING,
            List.of(Map.of("UID", "1", "val", "A"), Map.of("UID", "2", "val", "B")),
            List.of(Map.of("UID", "3", "val", "C"), Map.of("UID", "4", "val", "D")),
            List.of(Map.of("UID", "3", "val", "C"), Map.of("UID", "4", "val", "D")),
            2),
        // update with old keys
        Arguments.of(
            1032,
            WITH_VERSIONING,
            List.of(Map.of("UID", "1", "val", "A"), Map.of("UID", "2", "val", "B")),
            List.of(Map.of("UID", "1", "val", "X"), Map.of("UID", "2", "val", "Y")),
            List.of(Map.of("UID", "1", "val", "X"), Map.of("UID", "2", "val", "Y")),
            2),
        // update with some new keys
        Arguments.of(
            1033,
            WITH_VERSIONING,
            List.of(Map.of("UID", "1", "val", "A"), Map.of("UID", "2", "val", "B")),
            List.of(Map.of("UID", "2", "val", "B"), Map.of("UID", "3", "val", "C")),
            List.of(Map.of("UID", "2", "val", "B"), Map.of("UID", "3", "val", "C")),
            2),
        // update with empty entries
        Arguments.of(
            1034,
            WITH_VERSIONING,
            List.of(Map.of("UID", "1", "val", "A"), Map.of("UID", "2", "val", "B")),
            List.of(),
            List.of(),
            0),
        // update with all new keys
        Arguments.of(
            1035,
            WITHOUT_VERSIONING,
            List.of(Map.of("UID", "1", "val", "A"), Map.of("UID", "2", "val", "B")),
            List.of(Map.of("UID", "3", "val", "C"), Map.of("UID", "4", "val", "D")),
            List.of(
                Map.of("UID", "1", "val", "A"),
                Map.of("UID", "2", "val", "B"),
                Map.of("UID", "3", "val", "C"),
                Map.of("UID", "4", "val", "D")),
            4),
        // update with old keys
        Arguments.of(
            1036,
            WITHOUT_VERSIONING,
            List.of(Map.of("UID", "1", "val", "A"), Map.of("UID", "2", "val", "B")),
            List.of(Map.of("UID", "1", "val", "X"), Map.of("UID", "2", "val", "Y")),
            List.of(Map.of("UID", "1", "val", "X"), Map.of("UID", "2", "val", "Y")),
            4 // we can't track actual map size, it's actually a sum of all updates
            ),
        // update with some new keys
        Arguments.of(
            1037,
            WITHOUT_VERSIONING,
            List.of(Map.of("UID", "1", "val", "A"), Map.of("UID", "2", "val", "B")),
            List.of(Map.of("UID", "2", "val", "B"), Map.of("UID", "3", "val", "C")),
            List.of(
                Map.of("UID", "1", "val", "A"),
                Map.of("UID", "2", "val", "B"),
                Map.of("UID", "3", "val", "C")),
            4 // we can't track actual map size, it's actually a sum of all updates
            ),
        // update with empty entries
        Arguments.of(
            1038,
            WITHOUT_VERSIONING,
            List.of(Map.of("UID", "1", "val", "A"), Map.of("UID", "2", "val", "B")),
            List.of(),
            List.of(Map.of("UID", "1", "val", "A"), Map.of("UID", "2", "val", "B")),
            2 // we can't track actual map size, it's actually a sum of all updates
            ));
  }

  @ParameterizedTest
  @MethodSource("updateTestCases")
  @SneakyThrows
  public void testUpdate(
      int id,
      Boolean useVersioning,
      List<Map<String, String>> initialData,
      List<Map<String, String>> updateData,
      List<Map<String, String>> expectedData,
      int expectedMapSize) {
    DataMap dm = dataMap(id, useVersioning, EMIT_DEFAULTS, initialData, Map.of());
    Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, dm.getId());
    try {
      lookup.save(dm);
      DataMap updatedDm = dataMap(id, useVersioning, EMIT_DEFAULTS, updateData, Map.of());
      lookup.save(updatedDm);
      Thread.sleep(500); // wait for async delete

      MasterEntry master = lookup.getMasterEntry();
      String expectedVersion = useVersioning ? "2" : "1";
      assertEquals(expectedVersion, master.currentVersion);
      assertNull(master.nextVersion);
      assertEquals(expectedMapSize, master.mapSize);
      assertFalse(master.limitReached);

      assertEquals(
          expectedData.size(), lookup.countEntries(expectedVersion, master.dataModelVersion));
      for (Map<String, String> dmEntry : expectedData) {
        Map<String, String> entry = lookup.getEntry(expectedVersion, dmEntry.get("UID"));
        assertEquals(dmEntry.get("val"), entry.get("val"));
      }
      int expectedEntriesIndexCount =
          master.dataModelVersion == V1 || expectedData.isEmpty() ? 0 : 1;
      // master entry key + entries index key (optional) + entries keys
      int expectedRedisKeysCount = 1 + expectedEntriesIndexCount + expectedData.size();
      assertEquals(expectedRedisKeysCount, countMatching(dm.getId()));
    } finally {
      lookup.deleteAsync().get();
    }
  }

  protected int countMatching(int id) {
    String keyPattern = String.format("*%s*", id);
    ScanArgs params = ScanArgs.Builder.matches(keyPattern).limit(100);
    return JEDIS.scan(params).getKeys().size();
  }

  public static Stream<Arguments> beginUpdateTestCases() {
    return Stream.of(
        // use versioning = false, current version set
        Arguments.of(
            1021,
            Map.of(
                MasterEntry.EMIT_DEFAULTS,
                Boolean.TRUE.toString(),
                MasterEntry.USE_VERSIONING,
                Boolean.FALSE.toString(),
                MasterEntry.MAP_PRIMARY_KEY,
                "ID",
                MasterEntry.CURRENT_VERSION,
                "1",
                MasterEntry.MAP_SIZE,
                "0",
                MasterEntry.LIMIT_REACHED,
                Boolean.FALSE.toString()),
            Map.of(
                MasterEntry.EMIT_DEFAULTS, Boolean.TRUE.toString(),
                MasterEntry.USE_VERSIONING, Boolean.FALSE.toString(),
                MasterEntry.MAP_PRIMARY_KEY, "ID",
                MasterEntry.CURRENT_VERSION, "1",
                MasterEntry.NEXT_VERSION, "1",
                MasterEntry.MAP_SIZE, "0",
                MasterEntry.LIMIT_REACHED, Boolean.FALSE.toString())),
        // use versioning == null, current version set
        Arguments.of(
            1022,
            Map.of(
                MasterEntry.EMIT_DEFAULTS, Boolean.TRUE.toString(),
                MasterEntry.MAP_PRIMARY_KEY, "ID",
                MasterEntry.CURRENT_VERSION, "1",
                MasterEntry.MAP_SIZE, "0",
                MasterEntry.LIMIT_REACHED, Boolean.FALSE.toString()),
            Map.of(
                MasterEntry.EMIT_DEFAULTS, Boolean.TRUE.toString(),
                MasterEntry.MAP_PRIMARY_KEY, "ID",
                MasterEntry.CURRENT_VERSION, "1",
                MasterEntry.NEXT_VERSION, "1",
                MasterEntry.MAP_SIZE, "0",
                MasterEntry.LIMIT_REACHED, Boolean.FALSE.toString())),
        // use versioning set, current version == null
        Arguments.of(
            1023,
            Map.of(
                MasterEntry.EMIT_DEFAULTS, Boolean.TRUE.toString(),
                MasterEntry.USE_VERSIONING, Boolean.FALSE.toString(),
                MasterEntry.MAP_PRIMARY_KEY, "ID",
                MasterEntry.MAP_SIZE, "0",
                MasterEntry.LIMIT_REACHED, Boolean.FALSE.toString()),
            Map.of(
                MasterEntry.EMIT_DEFAULTS, Boolean.TRUE.toString(),
                MasterEntry.USE_VERSIONING, Boolean.FALSE.toString(),
                MasterEntry.MAP_PRIMARY_KEY, "ID",
                MasterEntry.CURRENT_VERSION, "1",
                MasterEntry.NEXT_VERSION, "1",
                MasterEntry.MAP_SIZE, "0",
                MasterEntry.LIMIT_REACHED, Boolean.FALSE.toString())),
        // map size > 0, limit reached = true
        Arguments.of(
            1024,
            Map.of(
                MasterEntry.EMIT_DEFAULTS,
                Boolean.TRUE.toString(),
                MasterEntry.USE_VERSIONING,
                Boolean.FALSE.toString(),
                MasterEntry.MAP_PRIMARY_KEY,
                "ID",
                MasterEntry.CURRENT_VERSION,
                "1",
                MasterEntry.MAP_SIZE,
                "10",
                MasterEntry.LIMIT_REACHED,
                Boolean.TRUE.toString()),
            Map.of(
                MasterEntry.EMIT_DEFAULTS, Boolean.TRUE.toString(),
                MasterEntry.USE_VERSIONING, Boolean.FALSE.toString(),
                MasterEntry.MAP_PRIMARY_KEY, "ID",
                MasterEntry.CURRENT_VERSION, "1",
                MasterEntry.NEXT_VERSION, "1",
                MasterEntry.MAP_SIZE, "10",
                MasterEntry.LIMIT_REACHED, Boolean.TRUE.toString())),
        // next version set
        Arguments.of(
            1025,
            Map.of(
                MasterEntry.EMIT_DEFAULTS, Boolean.TRUE.toString(),
                MasterEntry.USE_VERSIONING, Boolean.FALSE.toString(),
                MasterEntry.MAP_PRIMARY_KEY, "ID",
                MasterEntry.CURRENT_VERSION, "1",
                MasterEntry.NEXT_VERSION, "2",
                MasterEntry.MAP_SIZE, "0",
                MasterEntry.LIMIT_REACHED, Boolean.FALSE.toString()),
            Map.of(
                MasterEntry.EMIT_DEFAULTS, Boolean.TRUE.toString(),
                MasterEntry.USE_VERSIONING, Boolean.FALSE.toString(),
                MasterEntry.MAP_PRIMARY_KEY, "ID",
                MasterEntry.CURRENT_VERSION, "1",
                MasterEntry.NEXT_VERSION, "1",
                MasterEntry.MAP_SIZE, "0",
                MasterEntry.LIMIT_REACHED, Boolean.FALSE.toString())),
        // use versioning = true, current version set
        Arguments.of(
            1026,
            Map.of(
                MasterEntry.EMIT_DEFAULTS,
                Boolean.TRUE.toString(),
                MasterEntry.USE_VERSIONING,
                Boolean.TRUE.toString(),
                MasterEntry.MAP_PRIMARY_KEY,
                "ID",
                MasterEntry.CURRENT_VERSION,
                "1",
                MasterEntry.MAP_SIZE,
                "0",
                MasterEntry.LIMIT_REACHED,
                Boolean.FALSE.toString()),
            Map.of(
                MasterEntry.EMIT_DEFAULTS, Boolean.TRUE.toString(),
                MasterEntry.USE_VERSIONING, Boolean.TRUE.toString(),
                MasterEntry.MAP_PRIMARY_KEY, "ID",
                MasterEntry.CURRENT_VERSION, "1",
                MasterEntry.NEXT_VERSION, "2",
                MasterEntry.MAP_SIZE, "0",
                MasterEntry.LIMIT_REACHED, Boolean.FALSE.toString())),
        // use versioning = true, current version == null
        Arguments.of(
            1027,
            Map.of(
                MasterEntry.EMIT_DEFAULTS, Boolean.TRUE.toString(),
                MasterEntry.USE_VERSIONING, Boolean.TRUE.toString(),
                MasterEntry.MAP_PRIMARY_KEY, "ID",
                MasterEntry.MAP_SIZE, "0",
                MasterEntry.LIMIT_REACHED, Boolean.FALSE.toString()),
            Map.of(
                MasterEntry.EMIT_DEFAULTS,
                Boolean.TRUE.toString(),
                MasterEntry.USE_VERSIONING,
                Boolean.TRUE.toString(),
                MasterEntry.MAP_PRIMARY_KEY,
                "ID",
                MasterEntry.NEXT_VERSION,
                "1",
                MasterEntry.MAP_SIZE,
                "0",
                MasterEntry.LIMIT_REACHED,
                Boolean.FALSE.toString())),
        // use versioning = true, map size > 0, limit reached = true
        Arguments.of(
            1028,
            Map.of(
                MasterEntry.EMIT_DEFAULTS,
                Boolean.TRUE.toString(),
                MasterEntry.USE_VERSIONING,
                Boolean.TRUE.toString(),
                MasterEntry.MAP_PRIMARY_KEY,
                "ID",
                MasterEntry.CURRENT_VERSION,
                "1",
                MasterEntry.MAP_SIZE,
                "10",
                MasterEntry.LIMIT_REACHED,
                Boolean.TRUE.toString()),
            Map.of(
                MasterEntry.EMIT_DEFAULTS, Boolean.TRUE.toString(),
                MasterEntry.USE_VERSIONING, Boolean.TRUE.toString(),
                MasterEntry.MAP_PRIMARY_KEY, "ID",
                MasterEntry.CURRENT_VERSION, "1",
                MasterEntry.NEXT_VERSION, "2",
                MasterEntry.MAP_SIZE, "0",
                MasterEntry.LIMIT_REACHED, Boolean.FALSE.toString())),
        // use versioning = true, next version set
        Arguments.of(
            1029,
            Map.of(
                MasterEntry.EMIT_DEFAULTS, Boolean.TRUE.toString(),
                MasterEntry.USE_VERSIONING, Boolean.TRUE.toString(),
                MasterEntry.MAP_PRIMARY_KEY, "ID",
                MasterEntry.CURRENT_VERSION, "2",
                MasterEntry.NEXT_VERSION, "3",
                MasterEntry.MAP_SIZE, "0",
                MasterEntry.LIMIT_REACHED, Boolean.FALSE.toString()),
            Map.of(
                MasterEntry.EMIT_DEFAULTS, Boolean.TRUE.toString(),
                MasterEntry.USE_VERSIONING, Boolean.TRUE.toString(),
                MasterEntry.MAP_PRIMARY_KEY, "ID",
                MasterEntry.CURRENT_VERSION, "2",
                MasterEntry.NEXT_VERSION, "3",
                MasterEntry.MAP_SIZE, "0",
                MasterEntry.LIMIT_REACHED, Boolean.FALSE.toString())));
  }

  @ParameterizedTest
  @MethodSource("beginUpdateTestCases")
  @SneakyThrows
  public void testBeginUpdate(
      int id, Map<String, String> initialMasterEntry, Map<String, String> expectedMasterEntry) {
    Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, id);
    try {
      setMasterEntry(lookup, initialMasterEntry);
      lookup.beginUpdateEntries();
      expectedMasterEntry.forEach(
          (k, v) ->
              assertEquals(
                  v, lookup.getMasterEntryField(k), String.format("Wrong value for %s", k)));
    } finally {
      lookup.deleteAsync().get();
    }
  }

  private void setMasterEntry(Lookup lookup, Map<String, String> masterEntry) {
    if (!masterEntry.isEmpty()) {
      JEDIS.hmset("dm:master:" + lookup.getId(), masterEntry);
    }
  }

  public static Stream<Arguments> updateEntriesSuccessTestCases() {
    return Stream.of(
        // map size == 0
        Arguments.of(
            1041,
            Map.of(
                MasterEntry.MAP_PRIMARY_KEY, "UID",
                MasterEntry.CURRENT_VERSION, "1",
                MasterEntry.NEXT_VERSION, "2",
                MasterEntry.MAP_SIZE, "0",
                MasterEntry.LIMIT_REACHED, Boolean.FALSE.toString()),
            List.of(Map.of("UID", "1", "val", "A"), Map.of("UID", "2", "val", "B")),
            Map.of(
                MasterEntry.MAP_PRIMARY_KEY, "UID",
                MasterEntry.CURRENT_VERSION, "1",
                MasterEntry.NEXT_VERSION, "2",
                MasterEntry.MAP_SIZE, "2",
                MasterEntry.PREVIOUS_VERSION_MAP_SIZE, "0",
                MasterEntry.LIMIT_REACHED, Boolean.FALSE.toString()),
            List.of(Map.of("UID", "1", "val", "A"), Map.of("UID", "2", "val", "B")),
            2),
        // map size is not set
        Arguments.of(
            1042,
            Map.of(
                MasterEntry.MAP_PRIMARY_KEY, "UID",
                MasterEntry.CURRENT_VERSION, "1",
                MasterEntry.NEXT_VERSION, "2",
                MasterEntry.LIMIT_REACHED, Boolean.FALSE.toString()),
            List.of(Map.of("UID", "1", "val", "A"), Map.of("UID", "2", "val", "B")),
            Map.of(
                MasterEntry.MAP_PRIMARY_KEY, "UID",
                MasterEntry.CURRENT_VERSION, "1",
                MasterEntry.NEXT_VERSION, "2",
                MasterEntry.MAP_SIZE, "2",
                MasterEntry.LIMIT_REACHED, Boolean.FALSE.toString()),
            List.of(Map.of("UID", "1", "val", "A"), Map.of("UID", "2", "val", "B")),
            2),
        // map size > 0
        Arguments.of(
            1043,
            Map.of(
                MasterEntry.MAP_PRIMARY_KEY, "UID",
                MasterEntry.CURRENT_VERSION, "1",
                MasterEntry.NEXT_VERSION, "2",
                MasterEntry.MAP_SIZE, "2",
                MasterEntry.LIMIT_REACHED, Boolean.FALSE.toString()),
            List.of(Map.of("UID", "2", "val", "C"), Map.of("UID", "3", "val", "D")),
            Map.of(
                MasterEntry.MAP_PRIMARY_KEY, "UID",
                MasterEntry.CURRENT_VERSION, "1",
                MasterEntry.NEXT_VERSION, "2",
                MasterEntry.MAP_SIZE, "4",
                MasterEntry.PREVIOUS_VERSION_MAP_SIZE, "2",
                MasterEntry.LIMIT_REACHED, Boolean.FALSE.toString()),
            List.of(Map.of("UID", "2", "val", "C"), Map.of("UID", "3", "val", "D")),
            2),
        // invalid entries
        Arguments.of(
            1044,
            Map.of(
                MasterEntry.MAP_PRIMARY_KEY, "UID",
                MasterEntry.CURRENT_VERSION, "1",
                MasterEntry.NEXT_VERSION, "2",
                MasterEntry.MAP_SIZE, "5",
                MasterEntry.LIMIT_REACHED, Boolean.FALSE.toString()),
            List.of(
                Map.of(),
                Map.of("UID", "1"),
                Map.of("val", "B"),
                new HashMap<>() {
                  {
                    put("UID", "2");
                    put("val", null);
                  }
                },
                new HashMap<>() {
                  {
                    put("UID", "3");
                    put(null, "E");
                  }
                },
                new HashMap<>() {
                  {
                    put("UID", null);
                  }
                },
                Map.of("UID", "4", "val", "C"),
                Map.of("UID", "5", "val", "D")),
            Map.of(
                MasterEntry.MAP_PRIMARY_KEY, "UID",
                MasterEntry.CURRENT_VERSION, "1",
                MasterEntry.NEXT_VERSION, "2",
                MasterEntry.MAP_SIZE, "10",
                MasterEntry.PREVIOUS_VERSION_MAP_SIZE, "5",
                MasterEntry.LIMIT_REACHED, Boolean.FALSE.toString()),
            List.of(
                Map.of("UID", "1"),
                Map.of("UID", "2"),
                Map.of("UID", "3"),
                Map.of("UID", "4", "val", "C"),
                Map.of("UID", "5", "val", "D")),
            5),
        // empty update
        Arguments.of(
            1045,
            Map.of(
                MasterEntry.MAP_PRIMARY_KEY, "UID",
                MasterEntry.CURRENT_VERSION, "1",
                MasterEntry.NEXT_VERSION, "2",
                MasterEntry.MAP_SIZE, "5",
                MasterEntry.LIMIT_REACHED, Boolean.FALSE.toString()),
            List.of(),
            Map.of(
                MasterEntry.MAP_PRIMARY_KEY, "UID",
                MasterEntry.CURRENT_VERSION, "1",
                MasterEntry.NEXT_VERSION, "2",
                MasterEntry.MAP_SIZE, "5",
                MasterEntry.LIMIT_REACHED, Boolean.FALSE.toString()),
            List.of(),
            0));
  }

  @ParameterizedTest
  @MethodSource("updateEntriesSuccessTestCases")
  @SneakyThrows
  public void testUpdateEntriesSuccess(
      int id,
      Map<String, String> initialMasterEntry,
      List<Map<String, String>> entriesToUpdate,
      Map<String, String> expectedMasterEntry,
      List<Map<String, String>> expectedEntries,
      int expectedResult) {
    Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, id);
    try {
      setMasterEntry(lookup, initialMasterEntry);
      MasterEntry master = lookup.getMasterEntry();

      int result = lookup.updateEntries(entriesToUpdate);
      assertEquals(expectedResult, result);

      expectedMasterEntry.forEach(
          (k, v) ->
              assertEquals(
                  v, lookup.getMasterEntryField(k), String.format("Wrong value for %s", k)));

      expectedEntries.forEach(m -> assertEquals(m, lookup.getEntry("2", m.get("UID"))));

      long count = lookup.countEntries("2", master.dataModelVersion);
      assertEquals(expectedEntries.size(), count);
    } finally {
      lookup.deleteAsync().get();
    }
  }

  public static Stream<Arguments> updateEntriesFailureTestCases() {
    return Stream.of(
        // missing mater entry
        Arguments.of(
            1051, Map.of(), List.of(Map.of("UID", "1", "val", "A")), ValidationException.class),
        // missing primary key
        Arguments.of(
            1052,
            Map.of(
                MasterEntry.CURRENT_VERSION, "1",
                MasterEntry.NEXT_VERSION, "2",
                MasterEntry.MAP_SIZE, "2",
                MasterEntry.LIMIT_REACHED, Boolean.FALSE.toString()),
            List.of(Map.of("UID", "1", "val", "A")),
            ValidationException.class),
        // missing primary key
        Arguments.of(
            1053,
            Map.of(
                MasterEntry.MAP_PRIMARY_KEY, "UID",
                MasterEntry.CURRENT_VERSION, "1",
                MasterEntry.MAP_SIZE, "0",
                MasterEntry.LIMIT_REACHED, Boolean.FALSE.toString()),
            List.of(Map.of("UID", "1", "val", "A")),
            ValidationException.class),
        // limit reached
        Arguments.of(
            1054,
            Map.of(
                MasterEntry.MAP_PRIMARY_KEY, "UID",
                MasterEntry.CURRENT_VERSION, "1",
                MasterEntry.NEXT_VERSION, "1",
                MasterEntry.MAP_SIZE, "100",
                MasterEntry.LIMIT_REACHED, Boolean.TRUE.toString()),
            List.of(Map.of("UID", "1", "val", "A")),
            LimitReachedException.class));
  }

  @ParameterizedTest
  @MethodSource("updateEntriesFailureTestCases")
  @SneakyThrows
  public void testUpdateEntriesFailure(
      int id,
      Map<String, String> initialMasterEntry,
      List<Map<String, String>> entriesToUpdate,
      Class<Throwable> exception) {
    Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, id);
    try {
      setMasterEntry(lookup, initialMasterEntry);

      assertThrows(exception, () -> lookup.updateEntries(entriesToUpdate));
    } finally {
      lookup.deleteAsync().get();
    }
  }

  @Test
  @SneakyThrows
  public void testUpdateEntriesLimitReached() {
    Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, 1061);
    try {
      setMasterEntry(
          lookup,
          Map.of(
              MasterEntry.MAP_PRIMARY_KEY, "UID",
              MasterEntry.CURRENT_VERSION, "1",
              MasterEntry.NEXT_VERSION, "1",
              MasterEntry.MAP_SIZE_LIMIT, "2",
              MasterEntry.MAP_SIZE, "0",
              MasterEntry.LIMIT_REACHED, Boolean.FALSE.toString()));

      lookup.updateEntries(List.of(Map.of("UID", "1", "val", "B"), Map.of("UID", "2", "val", "C")));

      assertThrows(
          LimitReachedException.class,
          () -> lookup.updateEntries(List.of(Map.of("UID", "3", "val", "D"))));
      assertTrue(lookup.getMasterEntry().limitReached);
      assertEquals(2, lookup.getMasterEntry().mapSize);
      assertEquals(
          Set.of("dm:entry:1061:1:1", "dm:entry:1061:1:2"), Set.copyOf(lookup.getPrimaryKeys()));
    } finally {
      lookup.deleteAsync().get();
    }
  }

  @Test
  @SneakyThrows
  public void testUpdateEntriesLimitReached2() {
    Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, 1062);
    try {
      setMasterEntry(
          lookup,
          Map.of(
              MasterEntry.MAP_PRIMARY_KEY, "UID",
              MasterEntry.CURRENT_VERSION, "1",
              MasterEntry.NEXT_VERSION, "1",
              MasterEntry.MAP_SIZE_LIMIT, "3",
              MasterEntry.MAP_SIZE, "0",
              MasterEntry.LIMIT_REACHED, Boolean.FALSE.toString()));

      lookup.updateEntries(List.of(Map.of("UID", "1", "val", "A")));

      lookup.updateEntries(
          List.of(Map.of("UID", "1", "val", "A(update)"), Map.of("UID", "2", "val", "B")));

      assertFalse(lookup.getMasterEntry().limitReached);
      // it's the number of updates at this point
      assertEquals(3, lookup.getMasterEntry().mapSize);

      lookup.updateEntries(
          List.of(
              Map.of("UID", "3", "val", "C"),
              Map.of("UID", "4", "val", "D"),
              Map.of("UID", "2", "val", "B(update)")));

      assertFalse(lookup.getMasterEntry().limitReached);
      // still the number of updates, will be recalculated on next update
      assertEquals(5, lookup.getMasterEntry().mapSize);

      assertThrows(
          LimitReachedException.class,
          () -> lookup.updateEntries(List.of(Map.of("UID", "5", "val", "E"))));
      assertTrue(lookup.getMasterEntry().limitReached);
      assertEquals(4, lookup.getMasterEntry().mapSize);
    } finally {
      lookup.deleteAsync().get();
    }
  }

  private static final Boolean PREV_VERSION_ENTRIES_DELETED = true;
  private static final Boolean PREV_VERSION_ENTRIES_PRESENT = false;

  public static Stream<Arguments> endUpdateTestCases() {
    return Stream.of(
        Arguments.of(
            1071,
            Map.of(
                MasterEntry.USE_VERSIONING, Boolean.TRUE.toString(),
                MasterEntry.MAP_PRIMARY_KEY, "ID",
                MasterEntry.CURRENT_VERSION, "1",
                MasterEntry.NEXT_VERSION, "2"),
            new HashMap<>(
                Map.of(
                    MasterEntry.USE_VERSIONING,
                    Boolean.TRUE.toString(),
                    MasterEntry.MAP_PRIMARY_KEY,
                    "ID",
                    MasterEntry.CURRENT_VERSION,
                    "2")) {
              {
                put(MasterEntry.NEXT_VERSION, null);
              }
            },
            PREV_VERSION_ENTRIES_DELETED),
        Arguments.of(
            1072,
            Map.of(
                MasterEntry.USE_VERSIONING, Boolean.FALSE.toString(),
                MasterEntry.MAP_PRIMARY_KEY, "ID",
                MasterEntry.CURRENT_VERSION, "1",
                MasterEntry.NEXT_VERSION, "1"),
            new HashMap<>(
                Map.of(
                    MasterEntry.USE_VERSIONING,
                    Boolean.FALSE.toString(),
                    MasterEntry.MAP_PRIMARY_KEY,
                    "ID",
                    MasterEntry.CURRENT_VERSION,
                    "1")) {
              {
                put(MasterEntry.NEXT_VERSION, null);
              }
            },
            PREV_VERSION_ENTRIES_PRESENT),
        Arguments.of(
            1073,
            Map.of(
                MasterEntry.USE_VERSIONING, Boolean.FALSE.toString(),
                MasterEntry.MAP_PRIMARY_KEY, "ID",
                MasterEntry.CURRENT_VERSION, "1"),
            Map.of(
                MasterEntry.USE_VERSIONING, Boolean.FALSE.toString(),
                MasterEntry.MAP_PRIMARY_KEY, "ID",
                MasterEntry.CURRENT_VERSION, "1"),
            PREV_VERSION_ENTRIES_PRESENT));
  }

  @SneakyThrows
  @ParameterizedTest
  @MethodSource("endUpdateTestCases")
  public void testEndUpdate(
      int id,
      Map<String, String> initialMasterEntry,
      Map<String, String> expectedMasterEntry,
      boolean expectedEntriesDeleted) {
    Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, id);
    try {
      setMasterEntry(lookup, initialMasterEntry);
      MasterEntry master = lookup.getMasterEntry();
      lookup.setEntry("1", Map.of("UID", "3", "val", "C"));

      lookup.endUpdateEntries();

      expectedMasterEntry.forEach(
          (k, v) ->
              assertEquals(
                  v, lookup.getMasterEntryField(k), String.format("Wrong value for %s", k)));
      if (expectedEntriesDeleted) {
        Thread.sleep(500);
      }
      assertEquals(
          expectedEntriesDeleted,
          lookup.countEntries(
                  initialMasterEntry.get(MasterEntry.CURRENT_VERSION), master.dataModelVersion)
              == 0);
    } finally {
      lookup.deleteAsync().get();
    }
  }

  @SuppressWarnings("unchecked")
  @Test
  @SneakyThrows
  public void testDelete() {
    Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, 1081);
    try {
      lookup.save(
          dataMap(
              1081,
              WITH_VERSIONING,
              EMIT_DEFAULTS,
              List.of(Map.of("UID", "1", "val", "A"), Map.of("UID", "2", "val", "B")),
              Map.of("val", "n/a")));
      MasterEntry master = lookup.getMasterEntry();
      assertEquals(2, lookup.countEntries("1", master.dataModelVersion));
      assertFalse(master.isEmpty);
      Map<String, Object> masterAndDefaults = lookup.getMasterEntryAndDataDefaults();
      Map<String, String> masterEntry = (Map<String, String>) masterAndDefaults.get("master");
      Map<String, String> defaultsEntry = (Map<String, String>) masterAndDefaults.get("defaults");
      assertFalse(masterEntry.isEmpty());
      assertFalse(defaultsEntry.isEmpty());

      lookup.deleteAsync().get();
      assertEquals(0, lookup.countEntries("1", master.dataModelVersion));
      assertTrue(lookup.getMasterEntry().isEmpty);
      masterAndDefaults = lookup.getMasterEntryAndDataDefaults();
      masterEntry = (Map<String, String>) masterAndDefaults.get("master");
      defaultsEntry = (Map<String, String>) masterAndDefaults.get("defaults");
      assertTrue(masterEntry.isEmpty());
      assertTrue(defaultsEntry.isEmpty());
    } finally {
      lookup.deleteAsync().get();
    }
  }

  @Test
  @SneakyThrows
  public void testEntryOperations() {
    Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, 1091);
    try {
      lookup.save(
          dataMap(
              1091,
              WITH_VERSIONING,
              EMIT_DEFAULTS,
              List.of(
                  Map.of("UID", "one", "val", "A"),
                  Map.of("UID", "two", "val", "B"),
                  Map.of("UID", "three")),
              Map.of("val", "n/a")));

      MasterEntry master = lookup.getMasterEntry();
      log.info("Master entry: {}", master);
      // count
      assertEquals(3, lookup.countEntries("1", master.dataModelVersion));
      // get
      assertEquals(Map.of("UID", "two", "val", "B"), lookup.getEntry("1", "two"));
      // set (update)
      Map<String, String> newEntry = Map.of("UID", "four", "val", "W");
      lookup.setEntry("four", newEntry);
      assertEquals(newEntry, lookup.getEntry("1", "four"));
      assertEquals(4, lookup.countEntries("1", master.dataModelVersion));
      // set (update)
      Map<String, String> updateEntry = Map.of("UID", "two", "val", "X");
      lookup.setEntry("two", updateEntry);
      assertEquals(updateEntry, lookup.getEntry("1", "two"));
      // all entries
      assertEquals(
          Set.of(
              Map.of("UID", "one", "val", "A"),
              Map.of("UID", "two", "val", "X"),
              Map.of("UID", "three"),
              Map.of("UID", "four", "val", "W")),
          new HashSet<>(lookup.getAllEntries()));
      // filter entries
      assertEquals(
          Set.of(Map.of("UID", "one", "val", "A"), Map.of("UID", "three")),
          new HashSet<>(lookup.getEntriesByPattern("*e", false)));
      // filter single
      assertEquals(
          Set.of(Map.of("UID", "one", "val", "A")),
          new HashSet<>(lookup.getEntriesByPattern("one", true)));
      // primary keys
      assertEquals(
          Set.of(
              "dm:entry:1091:1:one",
              "dm:entry:1091:1:two",
              "dm:entry:1091:1:three",
              "dm:entry:1091:1:four"),
          lookup.getPrimaryKeys(master, master.currentVersion).toSet());
      assertEquals(
          Set.of(
              "dm:entry:1091:1:one",
              "dm:entry:1091:1:two",
              "dm:entry:1091:1:three",
              "dm:entry:1091:1:four"),
          new HashSet<>(lookup.getPrimaryKeys()));
      // delete
      lookup.deleteEntry("two");
      assertEquals(3, lookup.countEntries("1", master.dataModelVersion));
      assertFalse(lookup.getEntry("1", "one").isEmpty());
      assertTrue(lookup.getEntry("1", "two").isEmpty());
      // exist/hasDefaults
      assertTrue(lookup.exists());
      assertTrue(lookup.hasDefaults());
    } finally {
      lookup.deleteAsync().get();
    }
  }
}
