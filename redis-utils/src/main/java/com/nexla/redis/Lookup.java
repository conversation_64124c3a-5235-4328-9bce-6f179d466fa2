package com.nexla.redis;

import com.nexla.admin.client.DataMap;
import com.nexla.common.exception.NexlaException;
import com.nexla.redis.dto.MasterEntry;
import com.nexla.redis.exception.LookupMigrationException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import one.util.streamex.StreamEx;

public interface Lookup {
  int getId();

  void save(DataMap dataMap);

  void beginUpdateEntries();

  int updateEntries(List<Map<String, String>> entries) throws NexlaException;

  List<NexlaException> updateEntriesAndReturnErrors(List<Map<String, String>> entries)
      throws NexlaException;

  void endUpdateEntries();

  CompletableFuture<Void> deleteAsync();

  Boolean exists();

  Boolean hasDefaults();

  String getEntryKey(String version, String key);

  String getEntryKeyPrefix();

  Map<String, Object> getMasterEntryAndDataDefaults();

  MasterEntry getMasterEntry();

  Map<String, String> getEntry(String version, String key);

  List<String> getPrimaryKeys();

  StreamEx<String> getPrimaryKeys(MasterEntry masterEntry, String version);

  List<Map<String, String>> getAllEntries();

  List<Map<String, String>> getEntriesByPattern(String keyPattern, boolean exactMatch);

  StreamEx<Map<String, String>> getEntriesByPattern(
      MasterEntry masterEntry, String version, String keyPattern, boolean exactMatch);

  Map<String, String> setEntry(String key, Map<String, String> entry);

  boolean deleteEntry(String key);

  Long countEntries(String version, MasterEntry.DataModelVersion dataModelVersion);

  String getMasterEntryField(String fieldKey);

  void setMasterEntryField(String fieldKey, String fieldValue);

  void migrateDataModelVersion(
      MasterEntry.DataModelVersion fromVersion, MasterEntry.DataModelVersion toVersion)
      throws LookupMigrationException;

  void migrateDataModelVersion(
      MasterEntry.DataModelVersion fromVersion,
      MasterEntry.DataModelVersion toVersion,
      Set<String> allKeys)
      throws LookupMigrationException;

  void startDataModelVersionMigration(MasterEntry masterEntry) throws LookupMigrationException;

  void endDataModelVersionMigration();
}
