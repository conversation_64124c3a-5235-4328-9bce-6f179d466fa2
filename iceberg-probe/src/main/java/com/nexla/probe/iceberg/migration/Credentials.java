package com.nexla.probe.iceberg.migration;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Credentials {
  @JsonProperty("credentials_type")
  private String credentialsType;
  @JsonProperty("access_key_id")
  private String accessKeyId;
  @JsonProperty("secret_key")
  private String secretKey;
  @JsonProperty("region")
  private String region;

  public Credentials() {
    // For jackson deserialization
  }

  public Credentials(final String credentialsType, final String accessKeyId, final String secretKey, final String region) {
    this.credentialsType = credentialsType;
    this.accessKeyId = accessKeyId;
    this.secretKey = secretKey;
    this.region = region;
  }

}