package com.nexla.probe.iceberg.migration;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Lists;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileStatus;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.LocatedFileStatus;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.fs.RemoteIterator;
import org.apache.spark.sql.SparkSession;
import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Pattern;

/**
 * Migrates Aurora snapshots to Iceberg tables by referencing existing parquet files.
 * Processes tables in parallel across databases.
 */
public class AuroraSnapshotToIcebergMigrator {
  private static final Pattern PARQUET_PATTERN = Pattern.compile(".*\\.parquet$");
  private static final int PARALLEL_THREADS = 20;
  private static final int BATCH_PARQUET_FILES = 10_000;
  private static final ExecutorService FIXED_THREAD_POOL = Executors.newFixedThreadPool(PARALLEL_THREADS);

  private final RunIdLogger logger;
  private final SparkSession spark;
  private final String sourceSnapshotPath;
  private final String icebergWarehousePath;
  private final String catalogName;
  private final Configuration hadoopConf;

  public AuroraSnapshotToIcebergMigrator(
      final RunIdLogger logger,
      final SparkSession spark,
      final String sourceSnapshotPath,
      final String icebergWarehousePath,
      final String catalogName) {
    this.logger = logger;
    this.spark = spark;
    this.sourceSnapshotPath = normalizePath(sourceSnapshotPath);
    this.icebergWarehousePath = normalizePath(icebergWarehousePath);
    this.catalogName = catalogName;
    this.hadoopConf = spark.sparkContext().hadoopConfiguration();
  }

  private String normalizePath(String path) {
    return path.endsWith("/") ? path : path + "/";
  }

  /**
   * Starts the migration process with parallel table processing.
   */
  public void migrate() throws IOException {
    logger.info("Starting migration from {} to {}", sourceSnapshotPath, icebergWarehousePath);

    final long t1 = System.currentTimeMillis();
    // Scan source path to identify database and table structures
    final Map<String, List<String>> dbTableMap = scanDbTableStructure();
    logger.info("Scanned {} databases with tables in {} ms", dbTableMap.size(), System.currentTimeMillis() - t1);

    // Create all database namespaces first
    createAllDatabases(dbTableMap.keySet());

    // Submit table migration tasks to the thread pool
    for (final Map.Entry<String, List<String>> entry : dbTableMap.entrySet()) {
      final String dbName = entry.getKey();
      final List<String> tables = entry.getValue();

      logger.info("Queuing {} tables for database: {}", tables.size(), dbName);

      for (final String tableName : tables) {
        FIXED_THREAD_POOL.submit(() -> {
          try {
            migrateTable(dbName, tableName);
          } catch (final Exception e) {
            logger.error("Error migrating table {}.{}", dbName, tableName, e);
          }
        });
      }
    }

    logger.info("Migration completed successfully");
  }

  /**
   * Creates all database namespaces.
   *
   * @param dbNames Set of database names to create
   */
  private void createAllDatabases(Iterable<String> dbNames) {
    logger.info("Creating all database namespaces");

    for (String dbName : dbNames) {
      try {
        String createDbSQL = String.format("CREATE DATABASE IF NOT EXISTS %s.%s", catalogName, dbName);
        spark.sql(createDbSQL);
        logger.info("Created database namespace: {}.{}", catalogName, dbName);
      } catch (Exception e) {
        logger.error("Error creating database namespace {}.{}: {}", catalogName, dbName, e.getMessage(), e);
      }
    }
  }

  /**
   * Scans the snapshot directory to identify database and table combinations.
   *
   * @return Map of database names to list of table names
   */
  private Map<String, List<String>> scanDbTableStructure() throws IOException {
    Map<String, List<String>> dbTableMap = new HashMap<>();
    Path sourcePath = getSourcePath();
    FileSystem fs = sourcePath.getFileSystem(hadoopConf);

    // List all databases (directories) in the source path
    FileStatus[] dbDirs = fs.listStatus(sourcePath);

    for (FileStatus dbDir : dbDirs) {
      if (dbDir.isDirectory()) {
        String dbName = dbDir.getPath().getName();
        List<String> tables = new ArrayList<>();

        // List all tables in this database
        FileStatus[] tableDirs = fs.listStatus(dbDir.getPath());

        for (FileStatus tableDir : tableDirs) {
          if (tableDir.isDirectory()) {
            String fullTableName = tableDir.getPath().getName();
            // Expected format: <dbName>.<tableName>
            if (fullTableName.startsWith(dbName + ".")) {
              String tableName = fullTableName.substring(dbName.length() + 1);
              tables.add(tableName);
            }
          }
        }

        if (!tables.isEmpty()) {
          dbTableMap.put(dbName, tables);
        }
      }
    }

    return dbTableMap;
  }

  @VisibleForTesting
  @NotNull Path getSourcePath() {
    return new Path(sourceSnapshotPath);
  }

  /**
   * Migrates a specific table by creating an Iceberg table and adding all parquet files.
   *
   * @param dbName    Database name
   * @param tableName Table name
   */
  private void migrateTable(String dbName, String tableName) throws IOException {
    logger.info("Migrating table: {}.{}", dbName, tableName);

    // Construct paths
    String tableSourcePath = sourceSnapshotPath + dbName + "/" + dbName + "." + tableName + "/";
    String icebergTableName = catalogName + "." + dbName + "." + tableName;

    // Find all parquet files for this table
    List<String> parquetFiles = findParquetFiles(tableSourcePath);

    if (parquetFiles.isEmpty()) {
      logger.warn("No parquet files found for table {}.{}", dbName, tableName);
      return;
    }

    logger.info("Found {} parquet files for table {}.{}", parquetFiles.size(), dbName, tableName);

    // Create a ParquetToIcebergReferencer to add parquet files to Iceberg
    ParquetToIcebergReferencer referencer = new ParquetToIcebergReferencer(
        logger, spark, icebergWarehousePath, icebergTableName);

    List<List<String>> batchParquetFiles = Lists.partition(parquetFiles, BATCH_PARQUET_FILES);
    for (List<String> files : batchParquetFiles) {
      referencer.createTableWithReference(files);
    }

    logger.info("Successfully migrated table {}.{}", dbName, tableName);
  }

  /**
   * Finds all parquet files in the given path recursively.
   *
   * @param path Path to search for parquet files
   * @return List of parquet file paths
   */
  private List<String> findParquetFiles(String path) throws IOException {
    List<String> parquetFiles = new ArrayList<>();
    Path dirPath = new Path(path);
    FileSystem fs = dirPath.getFileSystem(hadoopConf);

    // Use recursive directory listing to find all files
    RemoteIterator<LocatedFileStatus> fileStatusListIterator = fs.listFiles(dirPath, true);

    while (fileStatusListIterator.hasNext()) {
      FileStatus fileStatus = fileStatusListIterator.next();
      if (!fileStatus.isDirectory()) {
        String filePath = fileStatus.getPath().toString();
        if (PARQUET_PATTERN.matcher(filePath).matches()) {
          parquetFiles.add(filePath);
        }
      }
    }
    return parquetFiles;
  }
}