import sys
import time

import single_service_build

def main():
    if len(sys.argv) != 2:
        print(f"Usage: {sys.argv[0]} <branch>")
        sys.exit(1)

    branch = sys.argv[1]

    # you can comment out lines that are already done if you need to re-run failed ones
    projects = [
        'http-sink',
        'kafka-connect-bigquery-sink',
        'kafka-connect-bigquery-source',
        'kafka-connect-file-sink',
        'kafka-connect-file-source',
        'kafka-connect-jdbc-sink',
        'kafka-connect-jdbc-source',
        'kafka-connect-documentdb-sink',
        'kafka-connect-documentdb-source',
        'kafka-connect-redis-sink',
        'kafka-connect-rest-sink',
        'kafka-connect-rest-source',
        'kafka-connect-soap-sink',
        'kafka-connect-soap-source',
        'kafka-connect-spreadsheets-sink',
        'kafka-connect-iceberg-source',
        'kafka-connect-iceberg-sink',
        'kafka-connect-api-streams-source',
        'kafka-connect-vectordb-source',
        'kafka-connect-vectordb-sink',
        'probe-http',
    ]

    for project in projects:
        print(f"Building project: {project}")
        status_code = single_service_build.build(branch, project)
        print(f"Queued build: {'yes' if status_code == 200 else 'no'}")
        print("Sleeping 90s")
        print("------------------------")
        time.sleep(90)
    print("All projects have been processed.")

if __name__ == '__main__':
    main()
