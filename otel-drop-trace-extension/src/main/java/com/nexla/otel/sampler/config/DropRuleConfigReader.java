package com.nexla.otel.sampler.config;

import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.trace.SpanKind;
import java.nio.file.Path;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import lombok.val;

public class DropRuleConfigReader {
  private static final Logger logger = Logger.getLogger(DropRuleConfigReader.class.getName());

  /**
   * Reads the sampler-drop-config.yaml file and returns a map containing drop rules indexed by
   * SpanKind.
   *
   * @return Map<SpanKind, Map<AttributeKey<String>, Set<String>>> where the inner map contains
   *     attribute keys and sets of pattern values
   */
  public Map<SpanKind, Map<AttributeKey<String>, Set<String>>> readDropRulesFromYaml(
      Path yamlFile) {
    Map<SpanKind, Map<AttributeKey<String>, Set<String>>> dropRulesBySpanKind = new HashMap<>();
    try {
      val dropRuleConfig = DropRuleConfig.fromYaml(yamlFile);

      for (val rule : dropRuleConfig.getDrop()) {
        val spanKind = rule.getSpanKind();
        if (spanKind == null) {
          logger.warning("Invalid rule: missing 'spanKind'");
          continue;
        }

        val attributes = rule.getAttributesAsMap();
        if (attributes.isEmpty()) {
          logger.warning("Invalid rule: missing or empty 'attributes'");
          continue;
        }

        val spanKindAttributes =
            dropRulesBySpanKind.computeIfAbsent(spanKind, k -> new HashMap<>());

        attributes.forEach(
            (key, patterns) ->
                spanKindAttributes
                    .computeIfAbsent(AttributeKey.stringKey(key), k -> new HashSet<>())
                    .addAll(patterns));
      }

      dropRulesBySpanKind.forEach(
          (spanKind, attributes) ->
              logger.info("Drop rules for SpanKind " + spanKind + ": " + attributes));

    } catch (Exception e) {
      logger.log(Level.WARNING, "Failed to load YAML config file: " + e.getMessage(), e);
    }

    return dropRulesBySpanKind;
  }
}
